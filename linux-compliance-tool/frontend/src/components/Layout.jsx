import React from 'react';
import './Layout.css';

const Layout = ({ children, activeTab, setActiveTab }) => {
  const tabs = [
    { id: 'servers', label: '🖥️ 服务器管理', icon: '🖥️' },
    { id: 'config', label: '⚙️ 核查配置', icon: '⚙️' },
    { id: 'task', label: '📋 任务管理', icon: '📋' },
    { id: 'report', label: '📄 报告导出', icon: '📄' }
  ];

  return (
    <div className="app-container">
      {/* 导航栏 */}
      <nav className="app-nav">
        {tabs.map(tab => (
          <button
            key={tab.id}
            className={`nav-tab ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </nav>

      {/* 主内容区域 */}
      <main className="app-main">
        <div className="content-container">
          {children}
        </div>
      </main>

      {/* 状态栏 */}
      <footer className="app-footer">
        <div className="footer-left">
          <span className="status-indicator">🟢 连接状态: 已连接</span>
        </div>
        <div className="footer-right">
          <span className="version-info">📦 版本: v1.0.0</span>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
