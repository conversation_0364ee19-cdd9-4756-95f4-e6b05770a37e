/* 应用容器 - 现代工具类风格 */
.app-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fafafa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
  color: #2c3e50;
}



/* 导航栏 - 现代工具类风格 */
.app-nav {
  height: 48px;
  background-color: #ffffff;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  align-items: center;
  padding: 0 16px;
  gap: 4px;
  flex-shrink: 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.nav-tab {
  background: transparent;
  border: none;
  border-bottom: 2px solid transparent;
  padding: 12px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  border-radius: 0;
  transition: all 0.2s ease;
  white-space: nowrap;
  position: relative;
}

.nav-tab:hover {
  background-color: #f8fafc;
  color: #475569;
}

.nav-tab.active {
  background-color: transparent;
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  font-weight: 600;
}

/* 主内容区域 - 现代工具类风格 */
.app-main {
  flex: 1;
  overflow: hidden;
  background-color: #fafafa;
}

.content-container {
  height: 100%;
  padding: 24px;
  overflow-y: auto;
  background-color: #fafafa;
}

/* 状态栏 - 现代工具类风格 */
.app-footer {
  height: 32px;
  background-color: #f8fafc;
  border-top: 1px solid #e1e5e9;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  font-size: 12px;
  color: #64748b;
  flex-shrink: 0;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.version-info {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* 统一滚动条样式 */
.content-container::-webkit-scrollbar,
.server-table-container::-webkit-scrollbar,
.config-table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.content-container::-webkit-scrollbar-track,
.server-table-container::-webkit-scrollbar-track,
.config-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.content-container::-webkit-scrollbar-thumb,
.server-table-container::-webkit-scrollbar-thumb,
.config-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.content-container::-webkit-scrollbar-thumb:hover,
.server-table-container::-webkit-scrollbar-thumb:hover,
.config-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
