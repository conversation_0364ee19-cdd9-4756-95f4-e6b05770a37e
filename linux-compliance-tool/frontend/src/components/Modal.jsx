import React from 'react';
import './Modal.css';

const Modal = ({ 
  isOpen, 
  onClose, 
  title, 
  children, 
  type = 'info', // info, success, warning, error
  showCloseButton = true,
  actions = null 
}) => {
  if (!isOpen) return null;

  const getIcon = () => {
    switch (type) {
      case 'success': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      case 'info':
      default: return 'ℹ️';
    }
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onClick={handleOverlayClick}>
      <div className={`modal-dialog modal-${type}`}>
        <div className="modal-header">
          <div className="modal-title">
            <span className="modal-icon">{getIcon()}</span>
            <span>{title}</span>
          </div>
          {showCloseButton && (
            <button className="modal-close-btn" onClick={onClose}>
              ✕
            </button>
          )}
        </div>
        
        <div className="modal-body">
          {children}
        </div>
        
        {actions && (
          <div className="modal-footer">
            {actions}
          </div>
        )}
      </div>
    </div>
  );
};

// 消息弹窗组件
export const MessageModal = ({ 
  isOpen, 
  onClose, 
  title, 
  message, 
  type = 'info',
  confirmText = '确定'
}) => {
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      type={type}
      actions={
        <button className="btn btn-primary" onClick={onClose}>
          {confirmText}
        </button>
      }
    >
      <p className="modal-message">{message}</p>
    </Modal>
  );
};

// 确认弹窗组件
export const ConfirmModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  title, 
  message, 
  type = 'warning',
  confirmText = '确定',
  cancelText = '取消'
}) => {
  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={title}
      type={type}
      actions={
        <>
          <button className="btn btn-secondary" onClick={onClose}>
            {cancelText}
          </button>
          <button className="btn btn-primary" onClick={handleConfirm}>
            {confirmText}
          </button>
        </>
      }
    >
      <p className="modal-message">{message}</p>
    </Modal>
  );
};

export default Modal;
