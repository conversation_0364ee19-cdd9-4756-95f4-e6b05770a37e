import React, { useState } from 'react';
import './TaskList.css';
import { MessageModal } from './Modal';
import CreateTaskDialog from './CreateTaskDialog';

// 编辑任务对话框组件
const EditTaskDialog = ({ isOpen, onClose, task, onSave }) => {
  const [editedTask, setEditedTask] = useState({
    name: task?.name || '',
    description: task?.description || ''
  });

  const handleSave = () => {
    if (editedTask.name.trim()) {
      onSave({
        id: task.id,
        name: editedTask.name,
        description: editedTask.description
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>✏️ 编辑任务</h3>
          <button className="modal-close" onClick={onClose}>✕</button>
        </div>
        <div className="modal-body">
          <div className="form-section">
            <h4>任务信息</h4>
            <div className="form-group">
              <label>任务名称 *</label>
              <input
                type="text"
                value={editedTask.name}
                onChange={(e) => setEditedTask({...editedTask, name: e.target.value})}
                placeholder="请输入任务名称"
              />
            </div>
            <div className="form-group">
              <label>任务描述</label>
              <textarea
                value={editedTask.description}
                onChange={(e) => setEditedTask({...editedTask, description: e.target.value})}
                placeholder="请输入任务描述（可选）"
                rows="3"
              />
            </div>
          </div>

          <div className="form-section">
            <h4>检查配置</h4>
            <div className="current-config">
              <div className="config-info">
                <div className="config-header">
                  <div className="config-name">当前配置</div>
                  <div className="config-meta">
                    <span className="item-count">32 项检查</span>
                  </div>
                </div>
                <div className="config-desc">使用核查配置页面设置的检查配置</div>
                <div className="config-note">
                  💡 如需修改检查配置，请前往"核查配置"页面进行设置
                </div>
              </div>
            </div>
          </div>

          <div className="form-section">
            <h4>任务状态</h4>
            <div className="task-status-info">
              <div className="status-item">
                <span className="status-label">当前状态:</span>
                <span className={`status-value status-${task?.status}`}>
                  {task?.status === 'pending' && '⏳ 等待执行'}
                  {task?.status === 'running' && '▶️ 执行中'}
                  {task?.status === 'completed' && '✅ 已完成'}
                  {task?.status === 'failed' && '❌ 执行失败'}
                  {task?.status === 'cancelled' && '⏹️ 已取消'}
                </span>
              </div>
              {task?.totalServers && (
                <div className="status-item">
                  <span className="status-label">目标服务器:</span>
                  <span className="status-value">{task.totalServers}台</span>
                </div>
              )}
            </div>
          </div>
        </div>
        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            ❌ 取消
          </button>
          <button className="btn btn-primary" onClick={handleSave}>
            ✅ 保存
          </button>
        </div>
      </div>
    </div>
  );
};

// 删除确认对话框组件
const DeleteConfirmDialog = ({ isOpen, onClose, task, onConfirm }) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>🗑️ 删除确认</h3>
          <button className="modal-close" onClick={onClose}>✕</button>
        </div>
        <div className="modal-body">
          <p>确定要删除任务 <strong>"{task?.name}"</strong> 吗？</p>
          <p className="warning-text">⚠️ 此操作不可撤销，请谨慎操作。</p>
        </div>
        <div className="modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            取消
          </button>
          <button className="btn btn-danger" onClick={onConfirm}>
            确认删除
          </button>
        </div>
      </div>
    </div>
  );
};

const TaskList = () => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);
  const [showMessage, setShowMessage] = useState(false);
  const [messageConfig, setMessageConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  const [tasks, setTasks] = useState([
    {
      id: 'task-001',
      name: '等保合规检查-生产环境',
      description: '对生产环境所有服务器进行等保合规检查',
      status: 'completed',
      createdAt: '2024-01-15T10:30:00Z',
      startedAt: '2024-01-15T10:35:00Z',
      completedAt: '2024-01-15T11:45:00Z',
      progress: 100,
      totalServers: 8,
      completedServers: 8,
      successCount: 6,
      warningCount: 2,
      errorCount: 0
    },
    {
      id: 'task-002',
      name: '等保合规检查-测试环境',
      description: '对测试环境服务器进行等保合规检查',
      status: 'running',
      createdAt: '2024-01-15T14:20:00Z',
      startedAt: '2024-01-15T14:25:00Z',
      completedAt: null,
      progress: 65,
      totalServers: 5,
      completedServers: 3,
      successCount: 2,
      warningCount: 1,
      errorCount: 0
    },
    {
      id: 'task-003',
      name: '等保合规检查-开发环境',
      description: '对开发环境服务器进行等保合规检查',
      status: 'pending',
      createdAt: '2024-01-15T16:10:00Z',
      startedAt: null,
      completedAt: null,
      progress: 0,
      totalServers: 3,
      completedServers: 0,
      successCount: 0,
      warningCount: 0,
      errorCount: 0
    }
  ]);

  const showMessageModal = (title, message, type = 'info') => {
    setMessageConfig({ title, message, type });
    setShowMessage(true);
  };

  const getStatusText = (status) => {
    const statusMap = {
      pending: '待执行',
      running: '执行中',
      completed: '已完成',
      failed: '执行失败',
      cancelled: '已取消'
    };
    return statusMap[status] || status;
  };

  const getStatusIcon = (status) => {
    const iconMap = {
      pending: '⏳',
      running: '🔄',
      completed: '✅',
      failed: '❌',
      cancelled: '⏹️'
    };
    return iconMap[status] || '❓';
  };

  const formatDateTime = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
  };

  const formatDuration = (startTime, endTime) => {
    if (!startTime) return '-';
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const duration = Math.floor((end - start) / 1000 / 60); // 分钟
    return `${duration}分钟`;
  };

  const handleCreateTask = () => {
    setShowCreateDialog(true);
  };

  const handleTaskCreated = (newTask) => {
    setTasks(prev => [newTask, ...prev]);
    setShowCreateDialog(false);
    showMessageModal('创建成功', '任务已创建，可以开始执行', 'success');
  };

  const handleStartTask = (taskId) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: 'running', startedAt: new Date().toISOString() }
        : task
    ));
    showMessageModal('任务启动', '任务已开始执行', 'info');
  };

  const handleStopTask = (taskId) => {
    setTasks(prev => prev.map(task => 
      task.id === taskId 
        ? { ...task, status: 'cancelled' }
        : task
    ));
    showMessageModal('任务停止', '任务已停止执行', 'warning');
  };

  const handleDeleteTask = (task) => {
    setSelectedTask(task);
    setShowDeleteDialog(true);
  };

  const confirmDeleteTask = () => {
    if (selectedTask) {
      setTasks(prev => prev.filter(task => task.id !== selectedTask.id));
      showMessageModal('删除成功', '任务已删除', 'success');
      setShowDeleteDialog(false);
      setSelectedTask(null);
    }
  };

  const handleEditTask = (task) => {
    setSelectedTask(task);
    setShowEditDialog(true);
  };

  const handleSaveEdit = (updatedTask) => {
    setTasks(prev => prev.map(task =>
      task.id === updatedTask.id ? { ...task, ...updatedTask } : task
    ));
    showMessageModal('编辑成功', '任务信息已更新', 'success');
    setShowEditDialog(false);
    setSelectedTask(null);
  };

  const handleViewDetails = (taskId) => {
    showMessageModal('查看详情', `查看任务 ${taskId} 的详细信息`, 'info');
  };

  return (
    <div className="task-list">
      <div className="page-header">
        <h2>📋 任务管理</h2>
        <div className="header-actions">
          <button className="btn btn-primary" onClick={handleCreateTask}>
            ➕ 创建任务
          </button>
          <button className="btn btn-secondary" onClick={() => showMessageModal('刷新', '正在刷新任务列表...', 'info')}>
            🔄 刷新
          </button>
        </div>
      </div>

      <div className="task-stats">
        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <div className="stat-info">
            <div className="stat-value">{tasks.length}</div>
            <div className="stat-label">总任务数</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">🔄</div>
          <div className="stat-info">
            <div className="stat-value">{tasks.filter(t => t.status === 'running').length}</div>
            <div className="stat-label">执行中</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">✅</div>
          <div className="stat-info">
            <div className="stat-value">{tasks.filter(t => t.status === 'completed').length}</div>
            <div className="stat-label">已完成</div>
          </div>
        </div>
        <div className="stat-card">
          <div className="stat-icon">⏳</div>
          <div className="stat-info">
            <div className="stat-value">{tasks.filter(t => t.status === 'pending').length}</div>
            <div className="stat-label">待执行</div>
          </div>
        </div>
      </div>

      <div className="task-table-container">
        <table className="task-table">
          <thead>
            <tr>
              <th width="200">任务名称</th>
              <th width="100">状态</th>
              <th width="80">进度</th>
              <th width="100">服务器</th>
              <th width="120">创建时间</th>
              <th width="100">执行时长</th>
              <th width="150">操作</th>
            </tr>
          </thead>
          <tbody>
            {tasks.map(task => (
              <tr key={task.id}>
                <td>
                  <div className="task-name">
                    <div className="name">{task.name}</div>
                    <div className="description">{task.description}</div>
                  </div>
                </td>
                <td>
                  <span className={`task-status status-${task.status}`}>
                    {getStatusIcon(task.status)} {getStatusText(task.status)}
                  </span>
                </td>
                <td>
                  <div className="progress-container">
                    <div className="progress-bar">
                      <div 
                        className="progress-fill" 
                        style={{ width: `${task.progress}%` }}
                      ></div>
                    </div>
                    <span className="progress-text">{task.progress}%</span>
                  </div>
                </td>
                <td>
                  <div className="server-info">
                    <div>{task.completedServers}/{task.totalServers}</div>
                    {task.status === 'completed' && (
                      <div className="server-results">
                        <span className="success">✅{task.successCount}</span>
                        <span className="warning">⚠️{task.warningCount}</span>
                        <span className="error">❌{task.errorCount}</span>
                      </div>
                    )}
                  </div>
                </td>
                <td>{formatDateTime(task.createdAt)}</td>
                <td>{formatDuration(task.startedAt, task.completedAt)}</td>
                <td>
                  <div className="task-actions">
                    <button 
                      className="btn btn-small btn-secondary" 
                      onClick={() => handleViewDetails(task.id)}
                    >
                      👁️ 详情
                    </button>
                    {(task.status === 'pending' || task.status === 'failed' || task.status === 'cancelled') && (
                      <button
                        className="btn btn-small btn-info"
                        onClick={() => handleEditTask(task)}
                      >
                        ✏️ 编辑
                      </button>
                    )}
                    {task.status === 'pending' && (
                      <button
                        className="btn btn-small btn-primary"
                        onClick={() => handleStartTask(task.id)}
                      >
                        ▶️ 启动
                      </button>
                    )}
                    {task.status === 'running' && (
                      <button
                        className="btn btn-small btn-warning"
                        onClick={() => handleStopTask(task.id)}
                      >
                        ⏹️ 停止
                      </button>
                    )}
                    {(task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') && (
                      <button
                        className="btn btn-small btn-danger"
                        onClick={() => handleDeleteTask(task)}
                      >
                        🗑️ 删除
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 创建任务对话框 */}
      <CreateTaskDialog
        isOpen={showCreateDialog}
        onClose={() => setShowCreateDialog(false)}
        onTaskCreated={handleTaskCreated}
      />

      {/* 编辑任务对话框 */}
      {showEditDialog && selectedTask && (
        <EditTaskDialog
          isOpen={showEditDialog}
          onClose={() => setShowEditDialog(false)}
          task={selectedTask}
          onSave={handleSaveEdit}
        />
      )}

      {/* 删除确认对话框 */}
      {showDeleteDialog && selectedTask && (
        <DeleteConfirmDialog
          isOpen={showDeleteDialog}
          onClose={() => setShowDeleteDialog(false)}
          task={selectedTask}
          onConfirm={confirmDeleteTask}
        />
      )}

      {/* 消息弹窗 */}
      <MessageModal
        isOpen={showMessage}
        onClose={() => setShowMessage(false)}
        title={messageConfig.title}
        message={messageConfig.message}
        type={messageConfig.type}
      />
    </div>
  );
};

export default TaskList;
