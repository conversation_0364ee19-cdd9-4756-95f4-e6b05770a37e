/* 创建任务对话框 */
.create-task-dialog {
  /* 移除内部滚动，让Modal组件处理滚动 */
}

.form-section {
  margin-bottom: 18px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f1f5f9;
}

.form-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.form-section h4 {
  margin: 0 0 12px 0;
  color: #1e293b;
  font-size: 15px;
  font-weight: 600;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.server-summary {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
}

/* 表单控件 */
.form-group {
  margin-bottom: 12px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-group input[type="text"],
.form-group textarea {
  width: 100%;
  padding: 6px 10px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 13px;
  transition: border-color 0.2s ease;
  font-family: inherit;
}

.form-group input[type="text"]:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 60px;
}

/* 当前配置显示 */
.current-config {
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background-color: #f8fafc;
}

.config-info {
  flex: 1;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.config-name {
  font-weight: 600;
  color: #1e293b;
  font-size: 15px;
}

.config-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.item-count {
  font-size: 12px;
  color: #3b82f6;
  background-color: #eff6ff;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.config-desc {
  font-size: 13px;
  color: #64748b;
  margin-bottom: 8px;
  line-height: 1.4;
}

.config-categories {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.config-note {
  font-size: 12px;
  color: #64748b;
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #fffbeb;
  border: 1px solid #fbbf24;
  border-radius: 4px;
}

/* 服务器操作 */
.server-actions {
  margin-bottom: 12px;
}

/* 服务器列表 */
.server-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-height: 160px;
  overflow-y: auto;
  padding: 4px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background-color: #fafafa;
}

.server-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 10px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.server-item:hover:not(.disabled) {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

.server-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f9fafb;
}

.server-info {
  flex: 1;
}

.server-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 2px;
}

.server-status {
  font-size: 12px;
}

.server-ip {
  font-size: 12px;
  color: #64748b;
}

/* 任务预览 */
.task-preview {
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  padding: 12px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.preview-item:last-child {
  margin-bottom: 0;
}

.preview-item .label {
  font-weight: 500;
  color: #374151;
}

.preview-item .value {
  color: #1e293b;
  font-weight: 500;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-family: inherit;
  outline: none;
}

.btn:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-small {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: 4px;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

.btn-secondary {
  background-color: #f8fafc;
  color: #475569;
  border-color: #e2e8f0;
}

.btn-secondary:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
}

/* 滚动条样式 - 只保留服务器列表的滚动条 */
.server-list::-webkit-scrollbar {
  width: 6px;
}

.server-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.server-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.server-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
