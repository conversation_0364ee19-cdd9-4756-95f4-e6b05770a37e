import React, { useState } from 'react';
import './ExportDialog.css';

const ExportDialog = ({ isOpen, onClose, task, onExport }) => {
  const [exportConfig, setExportConfig] = useState({
    reportType: 'complete',
    format: 'pdf',
    includeResults: true,
    includeDetails: true,
    includeSuggestions: true,
    includeLogs: false,
    savePath: '/home/<USER>/'
  });

  if (!isOpen) return null;

  const handleConfigChange = (key, value) => {
    setExportConfig(prev => ({ ...prev, [key]: value }));
  };

  const handleExport = () => {
    onExport(task, exportConfig);
    onClose();
  };

  const getReportTypeText = (type) => {
    switch (type) {
      case 'complete': return '完整报告';
      case 'issues': return '问题报告';
      case 'summary': return '摘要报告';
      default: return '完整报告';
    }
  };

  return (
    <div className="export-dialog-overlay" onClick={onClose}>
      <div className="export-dialog" onClick={(e) => e.stopPropagation()}>
        <div className="dialog-header">
          <h3>📄 导出报告</h3>
          <button className="close-btn" onClick={onClose}>×</button>
        </div>

        <div className="dialog-content">
          <div className="task-info-section">
            <h4>任务信息</h4>
            <div className="task-details">
              <div><strong>任务名称:</strong> {task?.name}</div>
              <div><strong>完成时间:</strong> {task?.completedAt ? new Date(task.completedAt).toLocaleString('zh-CN') : ''}</div>
              <div><strong>服务器数量:</strong> {task?.totalServers}台</div>
              <div><strong>合规率:</strong> {task?.compliance}%</div>
            </div>
          </div>

          <div className="config-section">
            <div className="config-row">
              <div className="config-item">
                <label>报告类型:</label>
                <div className="radio-group">
                  <label>
                    <input
                      type="radio"
                      value="complete"
                      checked={exportConfig.reportType === 'complete'}
                      onChange={(e) => handleConfigChange('reportType', e.target.value)}
                    />
                    完整报告
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="issues"
                      checked={exportConfig.reportType === 'issues'}
                      onChange={(e) => handleConfigChange('reportType', e.target.value)}
                    />
                    问题报告
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="summary"
                      checked={exportConfig.reportType === 'summary'}
                      onChange={(e) => handleConfigChange('reportType', e.target.value)}
                    />
                    摘要报告
                  </label>
                </div>
              </div>
            </div>

            <div className="config-row">
              <div className="config-item">
                <label>导出格式:</label>
                <div className="radio-group">
                  <label>
                    <input
                      type="radio"
                      value="pdf"
                      checked={exportConfig.format === 'pdf'}
                      onChange={(e) => handleConfigChange('format', e.target.value)}
                    />
                    PDF
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="excel"
                      checked={exportConfig.format === 'excel'}
                      onChange={(e) => handleConfigChange('format', e.target.value)}
                    />
                    Excel
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="word"
                      checked={exportConfig.format === 'word'}
                      onChange={(e) => handleConfigChange('format', e.target.value)}
                    />
                    Word
                  </label>
                </div>
              </div>
            </div>

            <div className="config-row">
              <div className="config-item">
                <label>包含内容:</label>
                <div className="checkbox-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={exportConfig.includeResults}
                      onChange={(e) => handleConfigChange('includeResults', e.target.checked)}
                    />
                    检查结果
                  </label>
                  <label>
                    <input
                      type="checkbox"
                      checked={exportConfig.includeDetails}
                      onChange={(e) => handleConfigChange('includeDetails', e.target.checked)}
                    />
                    问题详情
                  </label>
                  <label>
                    <input
                      type="checkbox"
                      checked={exportConfig.includeSuggestions}
                      onChange={(e) => handleConfigChange('includeSuggestions', e.target.checked)}
                    />
                    修复建议
                  </label>
                  <label>
                    <input
                      type="checkbox"
                      checked={exportConfig.includeLogs}
                      onChange={(e) => handleConfigChange('includeLogs', e.target.checked)}
                    />
                    执行日志
                  </label>
                </div>
              </div>
            </div>

            <div className="config-row">
              <div className="config-item">
                <label>保存位置:</label>
                <div className="file-path-input">
                  <input
                    type="text"
                    value={exportConfig.savePath}
                    onChange={(e) => handleConfigChange('savePath', e.target.value)}
                    placeholder="/home/<USER>/"
                  />
                  <button className="btn-file">📁</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="dialog-footer">
          <button className="btn btn-secondary" onClick={onClose}>
            取消
          </button>
          <button className="btn btn-primary" onClick={handleExport}>
            📄 开始导出
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExportDialog;
