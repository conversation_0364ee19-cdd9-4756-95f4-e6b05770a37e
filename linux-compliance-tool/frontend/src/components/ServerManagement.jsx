import React, { useState } from 'react';
import './ServerManagement.css';
import { MessageModal } from './Modal';

const ServerManagement = () => {
  const [servers, setServers] = useState([
    { id: 1, name: 'Web服务器01', ip: '*************', port: 22, status: 'online', selected: false },
    { id: 2, name: '数据库服务器', ip: '*************', port: 22, status: 'online', selected: false },
    { id: 3, name: '应用服务器01', ip: '*************', port: 22, status: 'offline', selected: false },
    { id: 4, name: '文件服务器', ip: '*************', port: 22, status: 'online', selected: false },
    { id: 5, name: '备份服务器', ip: '*************', port: 22, status: 'online', selected: false },
    { id: 6, name: '测试服务器', ip: '*************', port: 22, status: 'maintenance', selected: false },
    { id: 7, name: '开发服务器', ip: '*************', port: 22, status: 'online', selected: false },
    { id: 8, name: '监控服务器', ip: '*************', port: 22, status: 'online', selected: false },
    { id: 9, name: '日志服务器', ip: '*************', port: 22, status: 'online', selected: false },
    { id: 10, name: '缓存服务器', ip: '*************', port: 22, status: 'online', selected: false }
  ]);

  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingServer, setEditingServer] = useState(null);
  const [showMessage, setShowMessage] = useState(false);
  const [messageConfig, setMessageConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });
  const [testingServers, setTestingServers] = useState(new Set());
  const [newServer, setNewServer] = useState({
    name: '',
    ip: '',
    port: 22,
    username: 'root',
    authType: 'password',
    password: '',
    keyFile: '',
    keyPassphrase: ''
  });

  const getStatusIcon = (status) => {
    switch (status) {
      case 'online': return '🟢';
      case 'offline': return '🔴';
      case 'maintenance': return '🟡';
      default: return '⚪';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'online': return '在线';
      case 'offline': return '离线';
      case 'maintenance': return '维护';
      default: return '未知';
    }
  };

  const handleAddServer = () => {
    setShowAddDialog(true);
  };

  const handleSaveServer = () => {
    if (newServer.name && newServer.ip) {
      const server = {
        id: servers.length + 1,
        name: newServer.name,
        ip: newServer.ip,
        port: newServer.port,
        status: 'offline',
        selected: false
      };
      setServers([...servers, server]);
      setNewServer({
        name: '',
        ip: '',
        port: 22,
        username: 'root',
        authType: 'password',
        password: '',
        keyFile: '',
        keyPassphrase: ''
      });
      setShowAddDialog(false);
    }
  };

  const handleDeleteServer = (id) => {
    setServers(servers.filter(server => server.id !== id));
  };

  const showMessageModal = (title, message, type = 'info') => {
    setMessageConfig({ title, message, type });
    setShowMessage(true);
  };

  const handleTestConnection = async (server) => {
    setTestingServers(prev => new Set([...prev, server.id]));

    try {
      // 模拟测试连接过程
      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000));

      // 随机生成测试结果
      const success = Math.random() > 0.3;
      const newStatus = success ? 'online' : 'offline';

      // 更新服务器状态
      setServers(prev => prev.map(s =>
        s.id === server.id ? { ...s, status: newStatus } : s
      ));

      showMessageModal(
        '测试连接结果',
        `${server.name} (${server.ip}): ${success ? '✅ 连接成功' : '❌ 连接失败'}`,
        success ? 'success' : 'error'
      );
    } catch (error) {
      showMessageModal('测试连接', `测试 ${server.name} 时发生错误`, 'error');
    } finally {
      setTestingServers(prev => {
        const newSet = new Set(prev);
        newSet.delete(server.id);
        return newSet;
      });
    }
  };

  const handleBatchTestConnection = async () => {
    const selectedServers = servers.filter(s => s.selected);

    if (selectedServers.length === 0) {
      showMessageModal('提示', '请先选择要测试的服务器', 'warning');
      return;
    }

    showMessageModal('批量测试连接', `开始测试 ${selectedServers.length} 台服务器的连接...`, 'info');

    // 并发测试所有选中的服务器
    const testPromises = selectedServers.map(server => handleTestConnection(server));

    try {
      await Promise.all(testPromises);
      showMessageModal('批量测试完成', `已完成 ${selectedServers.length} 台服务器的连接测试`, 'success');
    } catch (error) {
      showMessageModal('批量测试', '部分服务器测试失败', 'warning');
    }
  };

  const selectAllServers = () => {
    const allSelected = servers.every(s => s.selected);
    setServers(servers.map(server => ({ ...server, selected: !allSelected })));
  };

  const handleEditServer = (server) => {
    setEditingServer({
      id: server.id,
      name: server.name,
      ip: server.ip,
      port: server.port,
      username: 'root', // 默认值，实际应该从服务器数据获取
      authType: 'password',
      password: '',
      keyFile: '',
      keyPassphrase: ''
    });
    setShowEditDialog(true);
  };

  const handleSaveEdit = () => {
    if (editingServer && editingServer.name && editingServer.ip) {
      setServers(prev => prev.map(server =>
        server.id === editingServer.id
          ? { ...server, name: editingServer.name, ip: editingServer.ip, port: editingServer.port }
          : server
      ));
      setShowEditDialog(false);
      setEditingServer(null);
      showMessageModal('编辑成功', '服务器信息已更新', 'success');
    }
  };

  const selectedCount = servers.filter(s => s.selected).length;
  const onlineCount = servers.filter(s => s.status === 'online').length;
  const offlineCount = servers.filter(s => s.status === 'offline').length;
  const maintenanceCount = servers.filter(s => s.status === 'maintenance').length;

  return (
    <div className="server-management">
      <div className="page-header">
        <h2>🖥️ 服务器管理</h2>
        <div className="header-actions">
          <button className="btn btn-primary" onClick={handleAddServer}>
            ➕ 添加服务器
          </button>
          <button
            className="btn btn-info"
            onClick={handleBatchTestConnection}
            disabled={selectedCount === 0}
          >
            🔗 测试连接
          </button>
          <button className="btn btn-secondary" onClick={selectAllServers}>
            {servers.every(s => s.selected) ? '☐ 取消全选' : '☑️ 全选'}
          </button>
          <button className="btn btn-secondary">
            🔄 刷新
          </button>
        </div>
      </div>

      {/* 服务器统计信息 */}
      <div className="server-stats">
        <div className="stat-item">
          <span className="stat-label">总计:</span>
          <span className="stat-value">{servers.length}台</span>
        </div>
        <div className="stat-divider">|</div>
        <div className="stat-item">
          <span className="stat-label">已选择:</span>
          <span className="stat-value">{selectedCount}台</span>
        </div>
        <div className="stat-divider">|</div>
        <div className="stat-item">
          <span className="stat-label">🟢 在线:</span>
          <span className="stat-value stat-online">{onlineCount}台</span>
        </div>
        <div className="stat-divider">|</div>
        <div className="stat-item">
          <span className="stat-label">🔴 离线:</span>
          <span className="stat-value stat-offline">{offlineCount}台</span>
        </div>
        <div className="stat-divider">|</div>
        <div className="stat-item">
          <span className="stat-label">🟡 维护:</span>
          <span className="stat-value stat-maintenance">{maintenanceCount}台</span>
        </div>
      </div>

      <div className="server-table-container">
        <table className="server-table">
          <thead>
            <tr>
              <th width="200">服务器名称</th>
              <th width="150">IP地址</th>
              <th width="80">端口</th>
              <th width="100">状态</th>
              <th width="200">操作</th>
            </tr>
          </thead>
          <tbody>
            {servers.map(server => (
              <tr key={server.id}>
                <td>{server.name}</td>
                <td>{server.ip}</td>
                <td>{server.port}</td>
                <td>
                  <span className={`status status-${server.status}`}>
                    {getStatusIcon(server.status)} {getStatusText(server.status)}
                  </span>
                </td>
                <td>
                  <div className="action-buttons">
                    <button
                      className="btn-small btn-edit"
                      onClick={() => handleEditServer(server)}
                    >
                      ✏️ 编辑
                    </button>
                    <button
                      className="btn-small btn-delete"
                      onClick={() => handleDeleteServer(server.id)}
                    >
                      🗑️ 删除
                    </button>
                    <button
                      className="btn-small btn-test"
                      onClick={() => handleTestConnection(server)}
                      disabled={testingServers.has(server.id)}
                    >
                      {testingServers.has(server.id) ? '⏳ 测试中' : '🔗 测试'}
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 添加服务器对话框 */}
      {showAddDialog && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>📝 添加服务器</h3>
              <button 
                className="modal-close"
                onClick={() => setShowAddDialog(false)}
              >
                ✕
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>服务器名称:</label>
                <input
                  type="text"
                  value={newServer.name}
                  onChange={(e) => setNewServer({...newServer, name: e.target.value})}
                  placeholder="Web服务器01"
                />
              </div>
              <div className="form-group">
                <label>IP地址:</label>
                <input
                  type="text"
                  value={newServer.ip}
                  onChange={(e) => setNewServer({...newServer, ip: e.target.value})}
                  placeholder="*************"
                />
              </div>
              <div className="form-group">
                <label>SSH端口:</label>
                <input
                  type="number"
                  value={newServer.port}
                  onChange={(e) => setNewServer({...newServer, port: parseInt(e.target.value)})}
                />
              </div>
              <div className="form-group">
                <label>用户名:</label>
                <input
                  type="text"
                  value={newServer.username}
                  onChange={(e) => setNewServer({...newServer, username: e.target.value})}
                />
              </div>
              <div className="form-group">
                <label>认证方式:</label>
                <div className="radio-group">
                  <label>
                    <input
                      type="radio"
                      value="password"
                      checked={newServer.authType === 'password'}
                      onChange={(e) => setNewServer({...newServer, authType: e.target.value})}
                    />
                    密码认证
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="key"
                      checked={newServer.authType === 'key'}
                      onChange={(e) => setNewServer({...newServer, authType: e.target.value})}
                    />
                    密钥认证
                  </label>
                </div>
              </div>
              {newServer.authType === 'password' ? (
                <div className="form-group">
                  <label>密码:</label>
                  <input
                    type="password"
                    value={newServer.password}
                    onChange={(e) => setNewServer({...newServer, password: e.target.value})}
                  />
                </div>
              ) : (
                <>
                  <div className="form-group">
                    <label>密钥文件:</label>
                    <div className="file-input">
                      <input
                        type="text"
                        value={newServer.keyFile}
                        onChange={(e) => setNewServer({...newServer, keyFile: e.target.value})}
                        placeholder="选择文件..."
                        readOnly
                      />
                      <button className="btn-file">📁</button>
                    </div>
                  </div>
                  <div className="form-group">
                    <label>密钥密码 (可选):</label>
                    <input
                      type="password"
                      value={newServer.keyPassphrase}
                      onChange={(e) => setNewServer({...newServer, keyPassphrase: e.target.value})}
                      placeholder="如果密钥有密码保护，请输入"
                    />
                  </div>
                </>
              )}
            </div>
            <div className="modal-footer">
              <button className="btn btn-test">🔍 测试连接</button>
              <button 
                className="btn btn-secondary"
                onClick={() => setShowAddDialog(false)}
              >
                ❌ 取消
              </button>
              <button 
                className="btn btn-primary"
                onClick={handleSaveServer}
              >
                ✅ 确定
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑服务器对话框 */}
      {showEditDialog && editingServer && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>✏️ 编辑服务器</h3>
              <button
                className="modal-close"
                onClick={() => setShowEditDialog(false)}
              >
                ✕
              </button>
            </div>
            <div className="modal-body">
              <div className="form-group">
                <label>服务器名称:</label>
                <input
                  type="text"
                  value={editingServer.name}
                  onChange={(e) => setEditingServer({...editingServer, name: e.target.value})}
                  placeholder="Web服务器01"
                />
              </div>
              <div className="form-group">
                <label>IP地址:</label>
                <input
                  type="text"
                  value={editingServer.ip}
                  onChange={(e) => setEditingServer({...editingServer, ip: e.target.value})}
                  placeholder="*************"
                />
              </div>
              <div className="form-group">
                <label>SSH端口:</label>
                <input
                  type="number"
                  value={editingServer.port}
                  onChange={(e) => setEditingServer({...editingServer, port: parseInt(e.target.value)})}
                />
              </div>
              <div className="form-group">
                <label>用户名:</label>
                <input
                  type="text"
                  value={editingServer.username}
                  onChange={(e) => setEditingServer({...editingServer, username: e.target.value})}
                />
              </div>
              <div className="form-group">
                <label>认证方式:</label>
                <div className="radio-group">
                  <label>
                    <input
                      type="radio"
                      value="password"
                      checked={editingServer.authType === 'password'}
                      onChange={(e) => setEditingServer({...editingServer, authType: e.target.value})}
                    />
                    密码认证
                  </label>
                  <label>
                    <input
                      type="radio"
                      value="key"
                      checked={editingServer.authType === 'key'}
                      onChange={(e) => setEditingServer({...editingServer, authType: e.target.value})}
                    />
                    密钥认证
                  </label>
                </div>
              </div>
              {editingServer.authType === 'password' ? (
                <div className="form-group">
                  <label>密码:</label>
                  <input
                    type="password"
                    value={editingServer.password}
                    onChange={(e) => setEditingServer({...editingServer, password: e.target.value})}
                  />
                </div>
              ) : (
                <>
                  <div className="form-group">
                    <label>密钥文件:</label>
                    <div className="file-input">
                      <input
                        type="text"
                        value={editingServer.keyFile}
                        onChange={(e) => setEditingServer({...editingServer, keyFile: e.target.value})}
                        placeholder="选择文件..."
                        readOnly
                      />
                      <button className="btn-file">📁</button>
                    </div>
                  </div>
                  <div className="form-group">
                    <label>密钥密码 (可选):</label>
                    <input
                      type="password"
                      value={editingServer.keyPassphrase}
                      onChange={(e) => setEditingServer({...editingServer, keyPassphrase: e.target.value})}
                      placeholder="如果密钥有密码保护，请输入"
                    />
                  </div>
                </>
              )}
            </div>
            <div className="modal-footer">
              <button className="btn btn-test">🔍 测试连接</button>
              <button
                className="btn btn-secondary"
                onClick={() => setShowEditDialog(false)}
              >
                ❌ 取消
              </button>
              <button
                className="btn btn-primary"
                onClick={handleSaveEdit}
              >
                ✅ 确定
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 消息弹窗 */}
      <MessageModal
        isOpen={showMessage}
        onClose={() => setShowMessage(false)}
        title={messageConfig.title}
        message={messageConfig.message}
        type={messageConfig.type}
      />
    </div>
  );
};

export default ServerManagement;
