/* 核查配置页面 */
.check-config {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.page-header h2 {
  margin: 0;
  color: #1e293b;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 配置表格 - 现代工具类风格 */
.config-table-container {
  flex: 1;
  overflow: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 24px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.config-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  font-size: 13px;
}

.config-table th,
.config-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #f1f5f9;
}

.config-table th {
  background-color: #f8fafc;
  font-weight: 600;
  color: #374151;
  position: sticky;
  top: 0;
  z-index: 1;
  border-bottom: 1px solid #e2e8f0;
  font-size: 12px;
  letter-spacing: 0.025em;
}

.config-table tbody tr {
  transition: background-color 0.15s ease;
}

.config-table tbody tr:hover {
  background-color: #f8fafc;
}

.config-table tbody tr:last-child td {
  border-bottom: none;
}

/* 级别标签 */
.level {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  min-width: 40px;
}

.level-high {
  background-color: #f8d7da;
  color: #721c24;
}

.level-medium {
  background-color: #fff3cd;
  color: #856404;
}

.level-low {
  background-color: #d4edda;
  color: #155724;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
  margin-right: 10px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #007bff;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.status-text {
  font-size: 12px;
  font-weight: 500;
}

.status-text.enabled {
  color: #28a745;
}

.status-text.disabled {
  color: #6c757d;
}

/* 统计信息 */
.config-stats {
  background-color: #f8f9fa;
  padding: 15px 20px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stats-row {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 14px;
  font-weight: 500;
}

.divider {
  color: #6c757d;
}

.stat-high {
  color: #dc3545;
}

.stat-medium {
  color: #ffc107;
}

.stat-low {
  color: #28a745;
}

.bulk-actions {
  display: flex;
  gap: 10px;
}

/* 按钮样式 - 原生桌面风格 */
.btn {
  padding: 6px 12px;
  border: 1px solid #a0a0a0;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.1s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.5);
}

.btn-primary {
  background: linear-gradient(180deg, #4a90e2 0%, #357abd 100%);
  color: white;
  border-color: #2968a3;
}

.btn-primary:hover {
  background: linear-gradient(180deg, #5ba0f2 0%, #4a90e2 100%);
}

.btn-primary:active {
  background: linear-gradient(180deg, #357abd 0%, #2968a3 100%);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.2);
}

.btn-secondary {
  background: linear-gradient(180deg, #f0f0f0 0%, #e0e0e0 100%);
  color: #333333;
  border-color: #a0a0a0;
}

.btn-secondary:hover {
  background: linear-gradient(180deg, #f8f8f8 0%, #e8e8e8 100%);
}

.btn-secondary:active {
  background: linear-gradient(180deg, #d8d8d8 0%, #e0e0e0 100%);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.btn-success {
  background: linear-gradient(180deg, #58d68d 0%, #27ae60 100%);
  color: white;
  border-color: #229954;
}

.btn-success:hover {
  background: linear-gradient(180deg, #82e5aa 0%, #58d68d 100%);
}

.btn-success:active {
  background: linear-gradient(180deg, #27ae60 0%, #229954 100%);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.2);
}

.btn-danger {
  background: linear-gradient(180deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  border-color: #a93226;
}

.btn-danger:hover {
  background: linear-gradient(180deg, #ec7063 0%, #e74c3c 100%);
}

.btn-danger:active {
  background: linear-gradient(180deg, #c0392b 0%, #a93226 100%);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.2);
}

/* 统一滚动条样式 */
.config-table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.config-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.config-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.config-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
