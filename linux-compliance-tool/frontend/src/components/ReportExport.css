/* 报告导出页面 */
.report-export {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #dee2e6;
}

.page-header h2 {
  margin: 0;
  color: #212529;
  font-size: 18px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 统计栏 */
.stats-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin-bottom: 12px;
  font-size: 13px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-label {
  font-weight: 500;
  color: #495057;
}

.stat-value {
  font-weight: 600;
  color: #212529;
}

.stat-normal {
  color: #28a745;
}

.stat-warning {
  color: #ffc107;
}

.stat-error {
  color: #dc3545;
}

.stat-divider {
  color: #6c757d;
}

/* 导出配置栏 */
.export-config-bar {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  padding: 10px 16px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.config-item label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.radio-group {
  display: flex;
  gap: 15px;
}

.radio-group label {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 0;
  cursor: pointer;
  font-weight: normal;
  font-size: 14px;
}

.checkbox-group {
  display: flex;
  gap: 15px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 0;
  cursor: pointer;
  font-weight: normal;
  font-size: 14px;
}

.file-path-input {
  display: flex;
  gap: 5px;
}

.file-path-input input {
  padding: 6px 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
}

.file-path-input input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.btn-file {
  padding: 6px 10px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.btn-file:hover {
  background-color: #545b62;
}

/* 任务表格 */
.task-table-container {
  flex: 1;
  overflow: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 15px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.task-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  font-size: 13px;
}

.task-table th,
.task-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #f1f5f9;
}

.task-table th {
  background-color: #f8fafc;
  font-weight: 600;
  color: #374151;
  position: sticky;
  top: 0;
  z-index: 1;
  border-bottom: 1px solid #e2e8f0;
  font-size: 12px;
  letter-spacing: 0.025em;
}

.task-table tbody tr {
  transition: background-color 0.15s ease;
}

.task-table tbody tr:hover {
  background-color: #f8fafc;
}

.task-table tbody tr:last-child td {
  border-bottom: none;
}

/* 任务信息 */
.task-info {
  max-width: 100%;
  overflow: hidden;
}

.task-info .task-name {
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  line-height: 1.4;
  font-size: 13px;
}

.task-info .task-desc {
  font-size: 11px;
  color: #64748b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.completion-time {
  font-size: 12px;
  color: #475569;
}

.task-count {
  font-size: 14px;
  color: #64748b;
  margin-right: 12px;
}

/* 导出按钮 */
.btn-export {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-export:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.server-count {
  font-weight: 500;
  color: #1e293b;
}

/* 检查结果摘要 */
.result-summary {
  display: flex;
  gap: 8px;
  font-size: 11px;
}

.result-summary .success {
  color: #059669;
}

.result-summary .warning {
  color: #d97706;
}

.result-summary .error {
  color: #dc2626;
}

/* 状态样式 */
.status {
  display: inline-flex;
  align-items: center;
  gap: 3px;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.status-normal {
  background-color: #d4edda;
  color: #155724;
}

.status-warning {
  background-color: #fff3cd;
  color: #856404;
}

.status-error {
  background-color: #f8d7da;
  color: #721c24;
}

/* 合规率样式 */
.compliance {
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 12px;
}

.compliance-good {
  background-color: #d4edda;
  color: #155724;
}

.compliance-medium {
  background-color: #fff3cd;
  color: #856404;
}

.compliance-poor {
  background-color: #f8d7da;
  color: #721c24;
}

.issue-count {
  display: inline-block;
  background-color: #dc3545;
  color: white;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

/* 任务操作区域 */
.task-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.selection-info {
  font-weight: 500;
  color: #475569;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

/* 按钮样式 - 原生桌面风格 */
.btn {
  padding: 6px 12px;
  border: 1px solid #a0a0a0;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
  transition: all 0.1s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.5);
}

.btn-small {
  padding: 4px 8px;
  font-size: 11px;
}

.btn-primary {
  background: linear-gradient(180deg, #4a90e2 0%, #357abd 100%);
  color: white;
  border-color: #2968a3;
}

.btn-primary:hover {
  background: linear-gradient(180deg, #5ba0f2 0%, #4a90e2 100%);
}

.btn-secondary {
  background: linear-gradient(180deg, #f0f0f0 0%, #e0e0e0 100%);
  color: #333333;
  border-color: #a0a0a0;
}

.btn-secondary:hover {
  background: linear-gradient(180deg, #f8f8f8 0%, #e8e8e8 100%);
}

.btn-info {
  background: linear-gradient(180deg, #5dade2 0%, #3498db 100%);
  color: white;
  border-color: #2980b9;
}

.btn-info:hover {
  background: linear-gradient(180deg, #85c1e9 0%, #5dade2 100%);
}

.btn-warning {
  background: linear-gradient(180deg, #f39c12 0%, #e67e22 100%);
  color: white;
  border-color: #d35400;
}

.btn-warning:hover {
  background: linear-gradient(180deg, #f7dc6f 0%, #f39c12 100%);
}

/* 统一滚动条样式 */
.server-table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.server-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.server-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.server-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
