/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-dialog {
  background-color: white;
  border-radius: 8px;
  min-width: 400px;
  max-width: 700px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 不同类型的模态框头部颜色 */
.modal-info .modal-header {
  background: linear-gradient(135deg, #17a2b8, #138496);
  color: white;
}

.modal-success .modal-header {
  background: linear-gradient(135deg, #28a745, #218838);
  color: white;
}

.modal-warning .modal-header {
  background: linear-gradient(135deg, #ffc107, #e0a800);
  color: #212529;
}

.modal-error .modal-header {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
}

.modal-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #dee2e6;
}

.modal-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
  font-weight: 600;
}

.modal-icon {
  font-size: 20px;
}

.modal-close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: inherit;
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 16px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.modal-close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.modal-body {
  padding: 16px 20px;
  max-height: calc(90vh - 160px);
  overflow-y: auto;
}

.modal-message {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
  color: #495057;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #f8f9fa;
}

/* 按钮样式 */
.modal-footer .btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.modal-footer .btn-primary {
  background-color: #007bff;
  color: white;
}

.modal-footer .btn-primary:hover {
  background-color: #0056b3;
}

.modal-footer .btn-secondary {
  background-color: #6c757d;
  color: white;
}

.modal-footer .btn-secondary:hover {
  background-color: #545b62;
}

.modal-footer .btn-success {
  background-color: #28a745;
  color: white;
}

.modal-footer .btn-success:hover {
  background-color: #218838;
}

.modal-footer .btn-danger {
  background-color: #dc3545;
  color: white;
}

.modal-footer .btn-danger:hover {
  background-color: #c82333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-dialog {
    min-width: 90vw;
    margin: 20px;
  }
  
  .modal-header {
    padding: 15px;
  }
  
  .modal-body {
    padding: 15px;
  }
  
  .modal-footer {
    padding: 15px;
    flex-direction: column;
  }
  
  .modal-footer .btn {
    width: 100%;
    justify-content: center;
  }
}
