import React, { useState } from 'react';
import './CreateTaskDialog.css';
import Modal from './Modal';

const CreateTaskDialog = ({ isOpen, onClose, onTaskCreated }) => {
  const [taskInfo, setTaskInfo] = useState({
    name: '',
    description: ''
  });

  const [servers, setServers] = useState([
    { id: 1, name: 'Web服务器01', ip: '************', status: 'online', selected: false },
    { id: 2, name: '数据库服务器', ip: '************', status: 'online', selected: false },
    { id: 3, name: '文件服务器', ip: '************', status: 'online', selected: false },
    { id: 4, name: '备份服务器', ip: '************', status: 'offline', selected: false },
    { id: 5, name: '开发服务器', ip: '************', status: 'online', selected: false },
    { id: 6, name: '测试服务器', ip: '************', status: 'online', selected: false }
  ]);

  // 使用当前的全局配置（从核查配置页面设置的配置）
  const currentConfig = {
    name: '当前配置',
    description: '使用核查配置页面设置的检查配置',
    itemCount: 32 // 这里应该从全局配置获取实际数量
  };

  const handleInputChange = (field, value) => {
    setTaskInfo(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const toggleServerSelection = (serverId) => {
    setServers(prev => prev.map(server => 
      server.id === serverId 
        ? { ...server, selected: !server.selected }
        : server
    ));
  };

  const selectAllServers = () => {
    const onlineServers = servers.filter(s => s.status === 'online');
    const allOnlineSelected = onlineServers.every(s => s.selected);
    
    setServers(prev => prev.map(server => 
      server.status === 'online' 
        ? { ...server, selected: !allOnlineSelected }
        : server
    ));
  };

  const handleCreate = () => {
    const selectedServers = servers.filter(s => s.selected);
    
    if (!taskInfo.name.trim()) {
      alert('请输入任务名称');
      return;
    }
    
    if (selectedServers.length === 0) {
      alert('请至少选择一台服务器');
      return;
    }

    const newTask = {
      id: `task-${Date.now()}`,
      name: taskInfo.name,
      description: taskInfo.description || '等保合规检查任务',
      status: 'pending',
      createdAt: new Date().toISOString(),
      startedAt: null,
      completedAt: null,
      progress: 0,
      totalServers: selectedServers.length,
      completedServers: 0,
      successCount: 0,
      warningCount: 0,
      errorCount: 0,
      servers: selectedServers,
      config: 'current' // 使用当前全局配置
    };

    onTaskCreated(newTask);
    
    // 重置表单
    setTaskInfo({ name: '', description: '' });
    setServers(prev => prev.map(s => ({ ...s, selected: false })));
  };

  const selectedCount = servers.filter(s => s.selected).length;
  const onlineCount = servers.filter(s => s.status === 'online').length;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="创建检查任务"
      type="info"
      actions={
        <>
          <button className="btn btn-secondary" onClick={onClose}>
            取消
          </button>
          <button className="btn btn-primary" onClick={handleCreate}>
            创建任务
          </button>
        </>
      }
    >
      <div className="create-task-dialog">
        {/* 任务基本信息 */}
        <div className="form-section">
          <h4>任务信息</h4>
          <div className="form-group">
            <label>任务名称 *</label>
            <input
              type="text"
              value={taskInfo.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="请输入任务名称"
            />
          </div>
          <div className="form-group">
            <label>任务描述</label>
            <textarea
              value={taskInfo.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="请输入任务描述（可选）"
              rows="3"
            />
          </div>
        </div>

        {/* 当前检查配置显示 */}
        <div className="form-section">
          <h4>检查配置</h4>
          <div className="current-config">
            <div className="config-info">
              <div className="config-header">
                <div className="config-name">{currentConfig.name}</div>
                <div className="config-meta">
                  <span className="item-count">{currentConfig.itemCount} 项检查</span>
                </div>
              </div>
              <div className="config-desc">{currentConfig.description}</div>
              <div className="config-note">
                💡 如需修改检查配置，请前往"核查配置"页面进行设置
              </div>
            </div>
          </div>
        </div>

        {/* 服务器选择 */}
        <div className="form-section">
          <div className="section-header">
            <h4>选择服务器</h4>
            <div className="server-summary">
              已选择 {selectedCount} / {onlineCount} 台在线服务器
            </div>
          </div>
          
          <div className="server-actions">
            <button 
              className="btn btn-small btn-secondary" 
              onClick={selectAllServers}
            >
              {servers.filter(s => s.status === 'online').every(s => s.selected) ? '取消全选' : '全选在线'}
            </button>
          </div>

          <div className="server-list">
            {servers.map(server => (
              <label key={server.id} className={`server-item ${server.status === 'offline' ? 'disabled' : ''}`}>
                <input
                  type="checkbox"
                  checked={server.selected}
                  disabled={server.status === 'offline'}
                  onChange={() => toggleServerSelection(server.id)}
                />
                <div className="server-info">
                  <div className="server-name">
                    {server.name}
                    <span className={`server-status status-${server.status}`}>
                      {server.status === 'online' ? '🟢' : '🔴'}
                    </span>
                  </div>
                  <div className="server-ip">{server.ip}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* 任务预览 */}
        {selectedCount > 0 && (
          <div className="form-section">
            <h4>任务预览</h4>
            <div className="task-preview">
              <div className="preview-item">
                <span className="label">任务名称:</span>
                <span className="value">{taskInfo.name || '未设置'}</span>
              </div>
              <div className="preview-item">
                <span className="label">检查配置:</span>
                <span className="value">{currentConfig.name}</span>
              </div>
              <div className="preview-item">
                <span className="label">目标服务器:</span>
                <span className="value">{selectedCount} 台</span>
              </div>
              <div className="preview-item">
                <span className="label">预计用时:</span>
                <span className="value">约 {selectedCount * 3} 分钟</span>
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default CreateTaskDialog;
