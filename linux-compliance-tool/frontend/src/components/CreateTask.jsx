import React, { useState } from 'react';
import './CreateTask.css';
import { MessageModal } from './Modal';

const CreateTask = () => {
  const [taskInfo, setTaskInfo] = useState({
    name: '等保合规检查_2024-01-15',
    description: '定期安全合规检查',
    executeType: 'immediate'
  });

  const [servers, setServers] = useState([
    { id: 1, name: 'Web服务器01', ip: '*************', status: 'online', lastCheck: '2024-01-14', selected: true },
    { id: 2, name: '数据库服务器', ip: '*************', status: 'online', lastCheck: '2024-01-14', selected: true },
    { id: 3, name: '应用服务器01', ip: '*************', status: 'offline', lastCheck: '2024-01-12', selected: false },
    { id: 4, name: '文件服务器', ip: '*************', status: 'online', lastCheck: '2024-01-14', selected: true },
    { id: 5, name: '备份服务器', ip: '*************', status: 'online', lastCheck: '2024-01-13', selected: true },
    { id: 6, name: '测试服务器', ip: '*************', status: 'maintenance', lastCheck: '2024-01-10', selected: false },
    { id: 7, name: '开发服务器', ip: '*************', status: 'online', lastCheck: '2024-01-14', selected: true },
    { id: 8, name: '监控服务器', ip: '*************', status: 'online', lastCheck: '2024-01-13', selected: false }
  ]);

  const [showProgressDialog, setShowProgressDialog] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentTask, setCurrentTask] = useState('');
  const [logs, setLogs] = useState([]);
  const [showMessage, setShowMessage] = useState(false);
  const [messageConfig, setMessageConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  const getStatusIcon = (status) => {
    switch (status) {
      case 'online': return '🟢';
      case 'offline': return '🔴';
      case 'maintenance': return '🟡';
      default: return '⚪';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'online': return '在线';
      case 'offline': return '离线';
      case 'maintenance': return '维护';
      default: return '未知';
    }
  };

  const toggleServerSelection = (id) => {
    setServers(servers.map(server => 
      server.id === id ? { ...server, selected: !server.selected } : server
    ));
  };

  const selectAll = () => {
    setServers(servers.map(server => ({ ...server, selected: true })));
  };

  const invertSelection = () => {
    setServers(servers.map(server => ({ ...server, selected: !server.selected })));
  };

  const showMessageModal = (title, message, type = 'info') => {
    setMessageConfig({ title, message, type });
    setShowMessage(true);
  };

  const refreshStatus = () => {
    showMessageModal('刷新状态', '正在刷新服务器状态...', 'info');
  };

  const previewTask = () => {
    const selectedServers = servers.filter(s => s.selected);
    const previewMessage = `任务名称: ${taskInfo.name}\n选中服务器: ${selectedServers.length}台\n预计用时: 约${selectedServers.length * 3}分钟`;
    showMessageModal('任务预览', previewMessage, 'info');
  };

  const startExecution = () => {
    const selectedServers = servers.filter(s => s.selected);
    if (selectedServers.length === 0) {
      showMessageModal('提示', '请至少选择一台服务器', 'warning');
      return;
    }
    
    setShowProgressDialog(true);
    setProgress(0);
    setCurrentTask('正在初始化检查任务...');
    setLogs([]);
    
    // 模拟执行过程
    simulateExecution(selectedServers);
  };

  const simulateExecution = (selectedServers) => {
    let currentProgress = 0;
    let logIndex = 0;
    const totalSteps = selectedServers.length * 3; // 每台服务器3个检查步骤
    
    const interval = setInterval(() => {
      currentProgress += 1;
      const progressPercent = Math.round((currentProgress / totalSteps) * 100);
      setProgress(progressPercent);
      
      const serverIndex = Math.floor(currentProgress / 3);
      const stepIndex = currentProgress % 3;
      
      if (serverIndex < selectedServers.length) {
        const server = selectedServers[serverIndex];
        const steps = ['用户权限检查', 'SSH配置检查', '防火墙配置检查'];
        const results = ['通过', '发现问题', '失败'];
        const icons = ['✅', '⚠️', '❌'];
        
        const step = steps[stepIndex];
        const result = results[Math.floor(Math.random() * 3)];
        const icon = icons[results.indexOf(result)];
        
        const time = new Date().toLocaleTimeString();
        const logEntry = `[${time}] ${icon} ${server.name} - ${step}${result}`;
        
        setLogs(prev => [...prev, logEntry]);
        setCurrentTask(`正在检查${server.name} - ${step}...`);
      }
      
      if (currentProgress >= totalSteps) {
        clearInterval(interval);
        setCurrentTask('检查完成');
        setProgress(100);
      }
    }, 1000);
  };

  const stopExecution = () => {
    setShowProgressDialog(false);
    setProgress(0);
    setCurrentTask('');
    setLogs([]);
  };

  const selectedCount = servers.filter(s => s.selected).length;
  const configStats = {
    total: 15,
    high: 6,
    medium: 5,
    low: 1,
    estimatedTime: selectedCount * 3,
    lastCompliance: 78
  };

  return (
    <div className="create-task">
      <div className="page-header">
        <h2>▶️ 创建检查任务</h2>
        <div className="header-actions">
          <button className="btn btn-info" onClick={previewTask}>
            🧪 预览任务
          </button>
          <button className="btn btn-primary" onClick={startExecution}>
            ▶️ 开始执行
          </button>
          <button className="btn btn-secondary">
            ❌ 取消
          </button>
        </div>
      </div>

      <div className="task-info-bar">
        <div className="info-item">
          <label>任务名称:</label>
          <input
            type="text"
            value={taskInfo.name}
            onChange={(e) => setTaskInfo({...taskInfo, name: e.target.value})}
            className="task-name-input"
          />
        </div>
        <div className="info-item">
          <label>任务描述:</label>
          <input
            type="text"
            value={taskInfo.description}
            onChange={(e) => setTaskInfo({...taskInfo, description: e.target.value})}
            className="task-desc-input"
          />
        </div>
        <div className="info-item">
          <label>执行时间:</label>
          <div className="radio-group">
            <label>
              <input
                type="radio"
                value="immediate"
                checked={taskInfo.executeType === 'immediate'}
                onChange={(e) => setTaskInfo({...taskInfo, executeType: e.target.value})}
              />
              立即执行
            </label>
            <label>
              <input
                type="radio"
                value="scheduled"
                checked={taskInfo.executeType === 'scheduled'}
                onChange={(e) => setTaskInfo({...taskInfo, executeType: e.target.value})}
              />
              定时执行
            </label>
          </div>
        </div>
        <div className="info-item config-preview">
          <div className="preview-stats">
            <span>📋 检查项: {configStats.total}项</span>
            <span>🔴 高危: {configStats.high}项</span>
            <span>🟡 中危: {configStats.medium}项</span>
            <span>🟢 低危: {configStats.low}项</span>
            <span>⏱️ 预计: {configStats.estimatedTime}分钟</span>
            <span>📊 上次合规率: {configStats.lastCompliance}%</span>
          </div>
        </div>
      </div>

      <div className="server-table-container">
        <table className="server-table">
          <thead>
            <tr>
              <th width="50">☑️</th>
              <th width="150">服务器名称</th>
              <th width="120">IP地址</th>
              <th width="80">状态</th>
              <th width="100">最后检查</th>
            </tr>
          </thead>
          <tbody>
            {servers.map(server => (
              <tr key={server.id}>
                <td>
                  <input
                    type="checkbox"
                    checked={server.selected}
                    onChange={() => toggleServerSelection(server.id)}
                  />
                </td>
                <td>{server.name}</td>
                <td>{server.ip}</td>
                <td>
                  <span className={`status status-${server.status}`}>
                    {getStatusIcon(server.status)} {getStatusText(server.status)}
                  </span>
                </td>
                <td>{server.lastCheck}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="server-actions">
        <div className="selection-info">
          已选择: {selectedCount}台服务器
        </div>
        <div className="action-buttons">
          <button className="btn btn-secondary" onClick={selectAll}>☑️ 全选</button>
          <button className="btn btn-secondary" onClick={invertSelection}>☐ 反选</button>
          <button className="btn btn-secondary" onClick={refreshStatus}>🔄 刷新状态</button>
        </div>
      </div>

      {/* 任务执行进度对话框 */}
      {showProgressDialog && (
        <div className="modal-overlay">
          <div className="progress-modal">
            <div className="modal-header">
              <h3>▶️ 执行检查任务</h3>
            </div>
            <div className="modal-body">
              <div className="progress-info">
                <div className="progress-bar-container">
                  <div className="progress-bar">
                    <div 
                      className="progress-fill" 
                      style={{width: `${progress}%`}}
                    ></div>
                  </div>
                  <span className="progress-text">{progress}% ({Math.floor(selectedCount * progress / 100)}/{selectedCount}服务器完成)</span>
                </div>
                <div className="current-task">
                  当前: {currentTask}
                </div>
              </div>
              
              <div className="log-section">
                <div className="log-header">
                  <span>📝 执行日志</span>
                  <button className="btn-small btn-secondary">🗑️ 清空</button>
                </div>
                <div className="log-container">
                  {logs.map((log, index) => (
                    <div key={index} className="log-entry">{log}</div>
                  ))}
                </div>
              </div>
            </div>
            <div className="modal-footer">
              <button className="btn btn-danger" onClick={stopExecution}>
                ⏹️ 停止检查
              </button>
              <button className="btn btn-primary">
                📋 查看结果
              </button>
              <button className="btn btn-secondary" onClick={() => setShowProgressDialog(false)}>
                ❌ 关闭
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 消息弹窗 */}
      <MessageModal
        isOpen={showMessage}
        onClose={() => setShowMessage(false)}
        title={messageConfig.title}
        message={messageConfig.message}
        type={messageConfig.type}
      />
    </div>
  );
};

export default CreateTask;
