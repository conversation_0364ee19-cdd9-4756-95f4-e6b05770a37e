import React, { useState } from 'react';
import './ReportExport.css';
import { MessageModal } from './Modal';
import ExportDialog from './ExportDialog';

const ReportExport = () => {
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);

  // 已完成的任务列表（用于生成报告）
  const [completedTasks, setCompletedTasks] = useState([
    {
      id: 'task-001',
      name: '等保合规检查-生产环境',
      description: '对生产环境所有服务器进行等保合规检查',
      completedAt: '2024-01-15T11:45:00Z',
      totalServers: 8,
      successCount: 6,
      warningCount: 2,
      errorCount: 0,
      compliance: 85,
      selected: true
    },
    {
      id: 'task-002',
      name: '等保合规检查-测试环境',
      description: '对测试环境服务器进行等保合规检查',
      completedAt: '2024-01-14T16:30:00Z',
      totalServers: 5,
      successCount: 3,
      warningCount: 2,
      errorCount: 0,
      compliance: 78,
      selected: false
    },
    {
      id: 'task-003',
      name: '网络安全专项检查',
      description: '专项网络安全配置检查',
      completedAt: '2024-01-13T14:20:00Z',
      totalServers: 12,
      successCount: 8,
      warningCount: 3,
      errorCount: 1,
      compliance: 72,
      selected: false
    }
  ]);

  const [showMessage, setShowMessage] = useState(false);
  const [messageConfig, setMessageConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  const getStatusIcon = (status) => {
    switch (status) {
      case 'normal': return '✅';
      case 'warning': return '⚠️';
      case 'error': return '❌';
      default: return '⚪';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'normal': return '正常';
      case 'warning': return '警告';
      case 'error': return '异常';
      default: return '未知';
    }
  };

  const showMessageModal = (title, message, type = 'info') => {
    setMessageConfig({ title, message, type });
    setShowMessage(true);
  };

  const handleExportTask = (task) => {
    setSelectedTask(task);
    setShowExportDialog(true);
  };

  const handleExport = (task, config) => {
    const reportMessage = `正在导出报告...\n任务: ${task.name}\n类型: ${getReportTypeText(config.reportType)}\n格式: ${config.format.toUpperCase()}\n保存位置: ${config.savePath}`;
    showMessageModal('导出报告', reportMessage, 'info');
  };

  const refreshTasks = () => {
    showMessageModal('刷新任务', '正在刷新已完成任务列表...', 'info');
  };

  const getReportTypeText = (type) => {
    switch (type) {
      case 'complete': return '完整报告';
      case 'issues': return '问题报告';
      case 'summary': return '摘要报告';
      default: return '完整报告';
    }
  };

  const totalTasks = completedTasks.length;

  return (
    <div className="report-export">
      <div className="page-header">
        <h2>📄 报告导出</h2>
        <div className="header-actions">
          <span className="task-count">共 {totalTasks} 个已完成任务</span>
          <button className="btn btn-info" onClick={refreshTasks}>
            🔄 刷新任务
          </button>
        </div>
      </div>

      <div className="task-table-container">
        <table className="task-table">
          <thead>
            <tr>
              <th width="300">任务名称</th>
              <th width="120">完成时间</th>
              <th width="80">服务器</th>
              <th width="80">合规率</th>
              <th width="120">检查结果</th>
              <th width="100">操作</th>
            </tr>
          </thead>
          <tbody>
            {completedTasks.map(task => (
              <tr key={task.id}>
                <td>
                  <div className="task-info">
                    <div className="task-name" title={task.name}>{task.name}</div>
                    <div className="task-desc" title={task.description}>{task.description}</div>
                  </div>
                </td>
                <td>
                  <div className="completion-time">
                    {new Date(task.completedAt).toLocaleString('zh-CN')}
                  </div>
                </td>
                <td>
                  <span className="server-count">{task.totalServers}台</span>
                </td>
                <td>
                  <span className={`compliance compliance-${task.compliance >= 80 ? 'good' : task.compliance >= 60 ? 'medium' : 'poor'}`}>
                    {task.compliance}%
                  </span>
                </td>
                <td>
                  <div className="result-summary">
                    <span className="success">✅{task.successCount}</span>
                    <span className="warning">⚠️{task.warningCount}</span>
                    <span className="error">❌{task.errorCount}</span>
                  </div>
                </td>
                <td>
                  <button
                    className="btn btn-export"
                    onClick={() => handleExportTask(task)}
                    title="导出报告"
                  >
                    📄 导出
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 导出配置对话框 */}
      <ExportDialog
        isOpen={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        task={selectedTask}
        onExport={handleExport}
      />

      {/* 消息弹窗 */}
      <MessageModal
        isOpen={showMessage}
        onClose={() => setShowMessage(false)}
        title={messageConfig.title}
        message={messageConfig.message}
        type={messageConfig.type}
      />
    </div>
  );
};

export default ReportExport;
