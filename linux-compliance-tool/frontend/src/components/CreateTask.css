/* 创建任务页面 */
.create-task {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #dee2e6;
}

.page-header h2 {
  margin: 0;
  color: #212529;
  font-size: 20px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* 任务信息栏 */
.task-info-bar {
  display: flex;
  gap: 20px;
  align-items: flex-end;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-item label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.task-name-input,
.task-desc-input {
  padding: 6px 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  min-width: 200px;
}

.task-name-input:focus,
.task-desc-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.radio-group {
  display: flex;
  gap: 15px;
}

.radio-group label {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 0;
  cursor: pointer;
  font-size: 14px;
}

.config-preview {
  flex: 1;
}

.preview-stats {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  font-size: 13px;
}

.preview-stats span {
  padding: 4px 8px;
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  font-weight: 500;
}

/* 服务器表格 */
.server-table-container {
  flex: 1;
  overflow: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin-bottom: 15px;
  background-color: white;
}

.server-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
}

.server-table th,
.server-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.server-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #495057;
  position: sticky;
  top: 0;
  z-index: 1;
}

.server-table tbody tr:hover {
  background-color: #f8f9fa;
}

/* 状态样式 */
.status {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-online {
  background-color: #d4edda;
  color: #155724;
}

.status-offline {
  background-color: #f8d7da;
  color: #721c24;
}

.status-maintenance {
  background-color: #fff3cd;
  color: #856404;
}

/* 服务器操作区域 */
.server-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.selection-info {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

/* 按钮样式 - 原生桌面风格 */
.btn {
  padding: 6px 12px;
  border: 1px solid #a0a0a0;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
  transition: all 0.1s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.5);
}

.btn-primary {
  background: linear-gradient(180deg, #4a90e2 0%, #357abd 100%);
  color: white;
  border-color: #2968a3;
}

.btn-primary:hover {
  background: linear-gradient(180deg, #5ba0f2 0%, #4a90e2 100%);
}

.btn-secondary {
  background: linear-gradient(180deg, #f0f0f0 0%, #e0e0e0 100%);
  color: #333333;
  border-color: #a0a0a0;
}

.btn-secondary:hover {
  background: linear-gradient(180deg, #f8f8f8 0%, #e8e8e8 100%);
}

.btn-info {
  background: linear-gradient(180deg, #5dade2 0%, #3498db 100%);
  color: white;
  border-color: #2980b9;
}

.btn-info:hover {
  background: linear-gradient(180deg, #85c1e9 0%, #5dade2 100%);
}

.btn-danger {
  background: linear-gradient(180deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  border-color: #a93226;
}

.btn-danger:hover {
  background: linear-gradient(180deg, #ec7063 0%, #e74c3c 100%);
}

.btn-small {
  padding: 4px 8px;
  font-size: 11px;
}

/* 进度对话框 */
.progress-modal {
  background-color: white;
  border-radius: 8px;
  width: 700px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
}

.modal-body {
  padding: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #dee2e6;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #f8f9fa;
}

/* 进度条 */
.progress-info {
  margin-bottom: 20px;
}

.progress-bar-container {
  margin-bottom: 10px;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #28a745, #20c997);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.current-task {
  font-size: 14px;
  color: #6c757d;
  font-style: italic;
}

/* 日志区域 */
.log-section {
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.log-header {
  padding: 10px 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.log-container {
  height: 200px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f9fa;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  margin-bottom: 5px;
  color: #495057;
}

.log-container::-webkit-scrollbar {
  width: 6px;
}

.log-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.log-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

/* 统一表格滚动条样式 */
.server-table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.server-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.server-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.server-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
