import React, { useState } from 'react';
import './CheckConfig.css';
import { MessageModal, ConfirmModal } from './Modal';

const CheckConfig = () => {
  const [checkItems, setCheckItems] = useState([
    { id: 1, category: '系统', name: '用户权限检查', level: '高危', enabled: true, description: '检查用户权限配置' },
    { id: 2, category: '系统', name: '密码策略检查', level: '中危', enabled: true, description: '检查密码复杂度策略' },
    { id: 3, category: '系统', name: '系统配置检查', level: '低危', enabled: false, description: '检查系统基础配置' },
    { id: 4, category: '网络', name: '防火墙配置检查', level: '高危', enabled: true, description: '检查防火墙规则配置' },
    { id: 5, category: '网络', name: '端口扫描检查', level: '中危', enabled: false, description: '扫描开放端口' },
    { id: 6, category: '网络', name: 'SSH配置检查', level: '高危', enabled: true, description: '检查SSH安全配置' },
    { id: 7, category: '应用', name: '服务配置检查', level: '中危', enabled: true, description: '检查关键服务配置' },
    { id: 8, category: '应用', name: '日志配置检查', level: '低危', enabled: true, description: '检查日志记录配置' },
    { id: 9, category: '应用', name: '文件权限检查', level: '高危', enabled: true, description: '检查文件权限设置' },
    { id: 10, category: '安全', name: '病毒扫描检查', level: '中危', enabled: false, description: '执行病毒扫描' },
    { id: 11, category: '安全', name: '进程检查', level: '低危', enabled: true, description: '检查异常进程' },
    { id: 12, category: '安全', name: '网络连接检查', level: '中危', enabled: true, description: '检查可疑网络连接' },
    { id: 13, category: '合规', name: '审计日志检查', level: '高危', enabled: true, description: '检查审计日志完整性' },
    { id: 14, category: '合规', name: '备份策略检查', level: '中危', enabled: true, description: '检查数据备份策略' },
    { id: 15, category: '合规', name: '访问控制检查', level: '高危', enabled: true, description: '检查访问控制策略' }
  ]);

  const categories = ['系统', '网络', '应用', '安全', '合规'];

  const [showMessage, setShowMessage] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const [messageConfig, setMessageConfig] = useState({
    title: '',
    message: '',
    type: 'info'
  });

  const getLevelColor = (level) => {
    switch (level) {
      case '高危': return 'high';
      case '中危': return 'medium';
      case '低危': return 'low';
      default: return 'low';
    }
  };

  const toggleItem = (id) => {
    setCheckItems(checkItems.map(item => 
      item.id === id ? { ...item, enabled: !item.enabled } : item
    ));
  };

  const enableAll = () => {
    setCheckItems(checkItems.map(item => ({ ...item, enabled: true })));
  };

  const disableAll = () => {
    setCheckItems(checkItems.map(item => ({ ...item, enabled: false })));
  };

  const showMessageModal = (title, message, type = 'info') => {
    setMessageConfig({ title, message, type });
    setShowMessage(true);
  };

  const saveConfig = () => {
    showMessageModal('保存成功', '检查配置已保存', 'success');
  };

  const resetDefault = () => {
    setShowConfirm(true);
  };

  const handleResetConfirm = () => {
    // 重置逻辑
    showMessageModal('重置成功', '已重置为默认配置', 'success');
  };

  const getStats = () => {
    const enabled = checkItems.filter(item => item.enabled).length;
    const high = checkItems.filter(item => item.enabled && item.level === '高危').length;
    const medium = checkItems.filter(item => item.enabled && item.level === '中危').length;
    const low = checkItems.filter(item => item.enabled && item.level === '低危').length;
    
    return { enabled, high, medium, low };
  };

  const stats = getStats();

  return (
    <div className="check-config">
      <div className="page-header">
        <h2>⚙️ 核查配置</h2>
        <div className="header-actions">
          <button className="btn btn-primary" onClick={saveConfig}>
            💾 保存配置
          </button>
          <button className="btn btn-secondary" onClick={resetDefault}>
            🔄 重置默认
          </button>
        </div>
      </div>

      <div className="config-table-container">
        <table className="config-table">
          <thead>
            <tr>
              <th width="80">分类</th>
              <th width="200">检查项名称</th>
              <th width="80">级别</th>
              <th width="80">状态</th>
              <th>描述</th>
            </tr>
          </thead>
          <tbody>
            {categories.map(category => (
              <React.Fragment key={category}>
                {checkItems
                  .filter(item => item.category === category)
                  .map(item => (
                    <tr key={item.id}>
                      <td>{item.category}</td>
                      <td>{item.name}</td>
                      <td>
                        <span className={`level level-${getLevelColor(item.level)}`}>
                          {item.level}
                        </span>
                      </td>
                      <td>
                        <label className="switch">
                          <input
                            type="checkbox"
                            checked={item.enabled}
                            onChange={() => toggleItem(item.id)}
                          />
                          <span className="slider"></span>
                        </label>
                        <span className={`status-text ${item.enabled ? 'enabled' : 'disabled'}`}>
                          {item.enabled ? '☑️启用' : '☐禁用'}
                        </span>
                      </td>
                      <td>{item.description}</td>
                    </tr>
                  ))}
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </div>

      <div className="config-stats">
        <div className="stats-row">
          <span>已启用: {stats.enabled}项</span>
          <span className="divider">|</span>
          <span className="stat-high">高危: {stats.high}项</span>
          <span className="divider">|</span>
          <span className="stat-medium">中危: {stats.medium}项</span>
          <span className="divider">|</span>
          <span className="stat-low">低危: {stats.low}项</span>
        </div>
        <div className="bulk-actions">
          <button className="btn btn-success" onClick={enableAll}>
            🔄 全部启用
          </button>
          <button className="btn btn-danger" onClick={disableAll}>
            ❌ 全部禁用
          </button>
        </div>
      </div>

      {/* 消息弹窗 */}
      <MessageModal
        isOpen={showMessage}
        onClose={() => setShowMessage(false)}
        title={messageConfig.title}
        message={messageConfig.message}
        type={messageConfig.type}
      />

      {/* 确认弹窗 */}
      <ConfirmModal
        isOpen={showConfirm}
        onClose={() => setShowConfirm(false)}
        onConfirm={handleResetConfirm}
        title="确认重置"
        message="确定要重置为默认配置吗？此操作将覆盖当前所有配置。"
        type="warning"
      />
    </div>
  );
};

export default CheckConfig;
