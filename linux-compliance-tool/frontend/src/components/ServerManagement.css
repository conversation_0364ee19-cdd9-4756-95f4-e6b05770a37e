/* 服务器管理页面 */
.server-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 20px 24px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.page-header h2 {
  margin: 0;
  color: #1e293b;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 服务器统计信息 */
.server-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background-color: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 13px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-weight: 500;
  color: #64748b;
}

.stat-value {
  font-weight: 600;
  color: #1e293b;
}

.stat-online {
  color: #059669;
}

.stat-offline {
  color: #dc2626;
}

.stat-maintenance {
  color: #d97706;
}

.stat-divider {
  color: #94a3b8;
}

/* 按钮样式 - 现代工具类风格 */
.btn {
  padding: 8px 16px;
  border: 1px solid transparent;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-family: inherit;
  outline: none;
  position: relative;
}

.btn:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
  border-color: #3b82f6;
}

.btn-primary:hover {
  background-color: #2563eb;
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background-color: #f8fafc;
  color: #475569;
  border-color: #e2e8f0;
}

.btn-secondary:hover {
  background-color: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-secondary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-danger {
  background-color: #ef4444;
  color: white;
  border-color: #ef4444;
}

.btn-danger:hover {
  background-color: #dc2626;
  border-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-danger:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

/* 服务器表格 - 现代工具类风格 */
.server-table-container {
  flex: 1;
  overflow: auto;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.server-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  font-size: 13px;
}

.server-table th,
.server-table td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #f1f5f9;
}

.server-table th {
  background-color: #f8fafc;
  font-weight: 600;
  color: #374151;
  position: sticky;
  top: 0;
  z-index: 1;
  border-bottom: 1px solid #e2e8f0;
  font-size: 12px;
  letter-spacing: 0.025em;
}

.server-table tbody tr {
  transition: background-color 0.15s ease;
}

.server-table tbody tr:hover {
  background-color: #f8fafc;
}

.server-table tbody tr:last-child td {
  border-bottom: none;
}

/* 状态样式 */
.status {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.status-online {
  background-color: #d4edda;
  color: #155724;
}

.status-offline {
  background-color: #f8d7da;
  color: #721c24;
}

.status-maintenance {
  background-color: #fff3cd;
  color: #856404;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 5px;
}

.btn-small {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.btn-edit {
  background-color: #0ea5e9;
  color: white;
  border-color: #0ea5e9;
}

.btn-edit:hover {
  background-color: #0284c7;
  border-color: #0284c7;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
}

.btn-delete {
  background-color: #ef4444;
  color: white;
  border-color: #ef4444;
}

.btn-delete:hover {
  background-color: #dc2626;
  border-color: #dc2626;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.btn-test {
  background-color: #10b981;
  color: white;
  border-color: #10b981;
}

.btn-test:hover:not(:disabled) {
  background-color: #059669;
  border-color: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn-test:disabled {
  background-color: #9ca3af;
  border-color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 模态框 - 原生桌面风格 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background-color: #f0f0f0;
  border: 2px outset #d0d0d0;
  width: 480px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 4px 4px 8px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 8px 12px;
  background: linear-gradient(180deg, #ffffff 0%, #e8e8e8 100%);
  border-bottom: 1px solid #a0a0a0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.8);
}

.modal-header h3 {
  margin: 0;
  color: #333333;
  font-size: 13px;
  font-weight: 500;
}

.modal-close {
  background: linear-gradient(180deg, #f0f0f0 0%, #e0e0e0 100%);
  border: 1px solid #a0a0a0;
  font-size: 12px;
  cursor: pointer;
  color: #333333;
  padding: 2px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.5);
}

.modal-close:hover {
  background: linear-gradient(180deg, #f8f8f8 0%, #e8e8e8 100%);
}

.modal-close:active {
  background: linear-gradient(180deg, #d8d8d8 0%, #e0e0e0 100%);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.modal-body {
  padding: 8px 12px;
  max-height: calc(90vh - 100px);
  overflow-y: auto;
  background-color: #ffffff;
}

.modal-footer {
  padding: 8px 12px;
  background: linear-gradient(180deg, #f0f0f0 0%, #e0e0e0 100%);
  border-top: 1px solid #a0a0a0;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.6);
}

/* Modal内按钮样式 */
.modal-footer .btn {
  padding: 6px 12px;
  border: 1px solid #a0a0a0;
  border-radius: 2px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 400;
  transition: all 0.1s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  box-shadow: inset 0 1px 0 rgba(255,255,255,0.5);
  min-width: 80px;
  justify-content: center;
}

.modal-footer .btn-primary {
  background: linear-gradient(180deg, #4a90e2 0%, #357abd 100%);
  color: white;
  border-color: #2968a3;
}

.modal-footer .btn-primary:hover {
  background: linear-gradient(180deg, #5ba0f2 0%, #4a90e2 100%);
}

.modal-footer .btn-primary:active {
  background: linear-gradient(180deg, #357abd 0%, #2968a3 100%);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.2);
}

.modal-footer .btn-secondary {
  background: linear-gradient(180deg, #f0f0f0 0%, #e0e0e0 100%);
  color: #333333;
  border-color: #a0a0a0;
}

.modal-footer .btn-secondary:hover {
  background: linear-gradient(180deg, #f8f8f8 0%, #e8e8e8 100%);
}

.modal-footer .btn-secondary:active {
  background: linear-gradient(180deg, #e0e0e0 0%, #d0d0d0 100%);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.1);
}

.modal-footer .btn-test {
  background: linear-gradient(180deg, #28a745 0%, #218838 100%);
  color: white;
  border-color: #1e7e34;
}

.modal-footer .btn-test:hover {
  background: linear-gradient(180deg, #34ce57 0%, #28a745 100%);
}

.modal-footer .btn-test:active {
  background: linear-gradient(180deg, #218838 0%, #1e7e34 100%);
  box-shadow: inset 0 1px 2px rgba(0,0,0,0.2);
}

/* 表单样式 */
.form-group {
  margin-bottom: 10px;
}

.form-group label {
  display: block;
  margin-bottom: 3px;
  font-weight: 500;
  color: #495057;
  font-size: 12px;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="password"] {
  width: 100%;
  padding: 4px 6px;
  border: 2px inset #d0d0d0;
  font-size: 12px;
  transition: border-color 0.1s;
  background-color: white;
}

.form-group input:focus {
  outline: none;
  border: 2px inset #4a90e2;
}

.radio-group {
  display: flex;
  gap: 15px;
}

.radio-group label {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 0;
  cursor: pointer;
  font-size: 12px;
}

.file-input {
  display: flex;
  gap: 5px;
}

.file-input input {
  flex: 1;
}

.btn-file {
  padding: 4px 8px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 2px;
  cursor: pointer;
  font-size: 11px;
}

.btn-file:hover {
  background-color: #545b62;
}

/* 统一滚动条样式 */
.server-table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.server-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.server-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.server-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
