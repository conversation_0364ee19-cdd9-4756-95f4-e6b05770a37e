import { useState } from 'react';
import './App.css';
import Layout from './components/Layout';
import ServerManagement from './components/ServerManagement';
import CheckConfig from './components/CheckConfig';
import TaskList from './components/TaskList';
import ReportExport from './components/ReportExport';

function App() {
  const [activeTab, setActiveTab] = useState('servers');

  const renderContent = () => {
    switch (activeTab) {
      case 'servers':
        return <ServerManagement />;
      case 'config':
        return <CheckConfig />;
      case 'task':
        return <TaskList />;
      case 'report':
        return <ReportExport />;
      default:
        return <ServerManagement />;
    }
  };

  return (
    <Layout activeTab={activeTab} setActiveTab={setActiveTab}>
      {renderContent()}
    </Layout>
  );
}

export default App;
