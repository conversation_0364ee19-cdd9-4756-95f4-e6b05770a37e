{"name": "@wailsapp/runtime", "version": "2.0.0", "description": "Wails Javascript runtime library", "main": "runtime.js", "types": "runtime.d.ts", "scripts": {}, "repository": {"type": "git", "url": "git+https://github.com/wailsapp/wails.git"}, "keywords": ["Wails", "Javascript", "Go"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/wailsapp/wails/issues"}, "homepage": "https://github.com/wailsapp/wails#readme"}