# Linux等保合规检查工具 - UI设计文档

## 总体设计原则

- **现代工具类风格**: 采用现代桌面应用设计语言，专业简洁
- **原生桌面体验**: 无内置标题栏，使用系统标题栏，更像原生应用
- **卡片化设计**: 使用白色卡片容器，清晰的层次结构和阴影效果
- **统一交互**: 现代按钮样式，微动画反馈，统一的颜色系统
- **响应式布局**: 灵活的内容区域，支持内容滚动

## 1. 主界面布局

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ 🛡️ Linux等保合规检查工具                                    [系统标题栏]      │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 🖥️ 服务器管理 │ ⚙️ 核查配置 │ 📋 任务管理 │ 📄 报告导出                      │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│                          主内容区域 (现代卡片式布局)                             │
│                                                                                 │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                                                                         │   │
│  │                        当前选中功能的内容                                │   │
│  │                     (白色卡片容器 + 阴影效果)                             │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  │                                                                         │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                                                                 │
├─────────────────────────────────────────────────────────────────────────────────┤
│ 🟢 连接状态: 已连接 │ 📦 版本: v1.0.0                                          │
└─────────────────────────────────────────────────────────────────────────────────┘
```

## 2. 服务器管理页面

```
  ┌─────────────────────────────────────────────────────────────────────────┐
  │ 🖥️ 服务器管理                        ➕ 添加服务器  🔄 刷新  🗑️ 删除   │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ ┌─────────────────────────────────────────────────────────────────────┐ │
  │ │服务器名称      │IP地址        │端口│状态  │操作              │       │ │
  │ ├─────────────────────────────────────────────────────────────────────┤ │
  │ │Web服务器01     │************* │22  │🟢在线│编辑 删除 测试     │       │ │
  │ │数据库服务器    │************* │22  │🟢在线│编辑 删除 测试     │       │ │
  │ │应用服务器01    │************* │22  │🔴离线│编辑 删除 测试     │       │ │
  │ │文件服务器      │************* │22  │🟢在线│编辑 删除 测试     │       │ │
  │ │备份服务器      │************* │22  │🟢在线│编辑 删除 测试     │       │ │
  │ │测试服务器      │************* │22  │🟡维护│编辑 删除 测试     │  ▲    │ │
  │ │开发服务器      │192.168.1.106 │22  │🟢在线│编辑 删除 测试     │  █    │ │
  │ │监控服务器      │192.168.1.107 │22  │🟢在线│编辑 删除 测试     │  ▼    │ │
  │ └─────────────────────────────────────────────────────────────────────┘ │
  └─────────────────────────────────────────────────────────────────────────┘
```

**设计特点:**
- 页面头部为白色卡片，包含标题和操作按钮
- 表格使用现代样式，圆角边框和阴影效果
- 按钮采用现代设计，悬停时有微动画效果
- 状态使用颜色标签清晰显示

## 3. 添加服务器对话框

```
                    ┌─────────────────────────────────────┐
                    │ ℹ️  添加服务器                    ✕ │
                    ├─────────────────────────────────────┤
                    │                                     │
                    │ 服务器名称: [Web服务器01_________]   │
                    │ IP地址:    [*************_______]   │
                    │ SSH端口:   [22]                     │
                    │ 用户名:    [root_______________]    │
                    │                                     │
                    │ 认证方式:                           │
                    │ ⚫ 密码认证  ⚪ 密钥认证             │
                    │                                     │
                    │ 密码:      [******************]    │
                    │ 密钥文件:  [选择文件...] 📁         │
                    │                                     │
                    │           取消        确定          │
                    └─────────────────────────────────────┘
```

**设计特点:**
- 现代模态对话框设计，带有类型图标和关闭按钮
- 表单控件使用现代样式，聚焦时有蓝色边框
- 按钮采用现代设计语言，主要操作使用蓝色

## 4. 核查配置页面

```
  ┌─────────────────────────────────────────────────────────────────────────┐
  │ ⚙️ 核查配置                              💾 保存配置  🔄 重置默认        │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ ┌─────────────────────────────────────────────────────────────────────┐ │
  │ │分类│检查项名称        │级别│状态│描述                            │   │ │
  │ ├─────────────────────────────────────────────────────────────────────┤ │
  │ │系统│用户权限检查      │高危│☑️启用│检查用户权限配置                │   │ │
  │ │系统│密码策略检查      │中危│☑️启用│检查密码复杂度策略              │   │ │
  │ │系统│系统配置检查      │低危│☐禁用│检查系统基础配置                │   │ │
  │ │网络│防火墙配置检查    │高危│☑️启用│检查防火墙规则配置              │   │ │
  │ │网络│端口扫描检查      │中危│☐禁用│扫描开放端口                    │   │ │
  │ │网络│SSH配置检查       │高危│☑️启用│检查SSH安全配置                │   │ │
  │ │应用│服务配置检查      │中危│☑️启用│检查关键服务配置                │   │ │
  │ │应用│日志配置检查      │低危│☑️启用│检查日志记录配置                │  ▲│ │
  │ │应用│文件权限检查      │高危│☑️启用│检查文件权限设置                │  █│ │
  │ │安全│病毒扫描检查      │中危│☐禁用│执行病毒扫描                    │  ▼│ │
  │ │安全│进程检查          │低危│☑️启用│检查异常进程                    │   │ │
  │ │安全│网络连接检查      │中危│☑️启用│检查可疑网络连接                │   │ │
  │ │合规│审计日志检查      │高危│☑️启用│检查审计日志完整性              │   │ │
  │ │合规│备份策略检查      │中危│☑️启用│检查数据备份策略                │   │ │
  │ │合规│访问控制检查      │高危│☑️启用│检查访问控制策略                │   │ │
  │ └─────────────────────────────────────────────────────────────────────┘ │
  │                                                                         │
  │ 已启用: 12项  │  高危: 6项  │  中危: 5项  │  低危: 1项                  │
  │                                                                         │
  │                    🔄 全部启用    ❌ 全部禁用                            │
  └─────────────────────────────────────────────────────────────────────────┘
```

**设计特点:**
- 页面头部卡片包含标题和操作按钮
- 配置表格使用现代样式，清晰的行分隔
- 统计信息栏显示配置摘要
- 批量操作按钮便于快速配置

## 5. 任务管理页面

  ┌─────────────────────────────────────────────────────────────────────────┐
  │ 📋 任务管理                                ➕ 创建任务  🔄 刷新         │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐                                        │
  │ │📊 3 │ │� 1 │ │✅ 1 │ │⏳ 1 │                                        │
  │ │总任务│ │执行中│ │已完成│ │待执行│                                        │
  │ └─────┘ └─────┘ └─────┘ └─────┘                                        │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ ┌─────────────────────────────────────────────────────────────────────┐ │
  │ │任务名称          │状态  │进度│服务器│创建时间    │操作        │       │ │
  │ ├─────────────────────────────────────────────────────────────────────┤ │
  │ │等保合规检查-生产环境│✅已完成│100%│8/8   │2024-01-15  │👁️详情 �️删除│ │
  │ │等保合规检查-测试环境│🔄执行中│65% │3/5   │2024-01-15  │👁️详情 ⏹️停止│ │
  │ │等保合规检查-开发环境│⏳待执行│0%  │0/3   │2024-01-15  │👁️详情 ▶️启动│ │
  │ └─────────────────────────────────────────────────────────────────────┘ │
  │                                                                         │
  │ 已选择: 1个任务                    ☑️ 全选  ☐ 反选                      │
  └─────────────────────────────────────────────────────────────────────────┘
```

**设计特点:**
- 统计卡片显示任务概览，图标+数值+标签的清晰布局
- 任务表格显示详细信息，包含进度条和状态标签
- 操作按钮根据任务状态动态显示
- 现代卡片式设计，清晰的视觉层次

## 6. 创建任务对话框

```
┌─────────────────────────────────────────────────────────────────────────────────┐
│ ℹ️  创建检查任务                                                            ✕ │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│ 任务信息                                                                        │
│ 任务名称: [等保合规检查-生产环境_________________________]                      │
│ 任务描述: [对生产环境所有服务器进行等保合规检查___________]                      │
│                                                                                 │
│ 检查配置                                                                        │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │ 当前配置                                              32 项检查              │ │
│ │ 使用核查配置页面设置的检查配置                                               │ │
│ │ � 如需修改检查配置，请前往"核查配置"页面进行设置                            │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│ 选择服务器                                                已选择 3 / 6 台在线服务器 │
│ ┌─────────────────────────────────────────────────────────────────────────────┐ │
│ │☑️ Web服务器01      *************  🟢                                        │ │
│ │☑️ 数据库服务器     *************  🟢                                        │ │
│ │☐ 文件服务器       *************  🔴                                        │ │
│ │☑️ 备份服务器       *************  🟢                                        │ │
│ └─────────────────────────────────────────────────────────────────────────────┘ │
│                                                                                 │
│ 任务预览                                                                        │
│ 任务名称: 等保合规检查-生产环境    检查配置: 当前配置                            │
│ 目标服务器: 3 台                  预计用时: 约 9 分钟                          │
│                                                                                 │
│                                    取消        创建任务                         │
└─────────────────────────────────────────────────────────────────────────────────┘
```

**设计特点:**
- 现代模态对话框，分区域显示不同信息
- 检查配置显示当前全局配置，避免重复选择
- 服务器列表紧凑显示，支持滚动
- 任务预览提供配置摘要确认

## 7. 报告导出页面

```
  ┌─────────────────────────────────────────────────────────────────────────┐
  │ 📄 报告导出                                📄 生成报告  🔄 刷新任务      │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ 总任务: 3个  │  已选择: 1个  │  总服务器: 25台  │  ✅ 正常: 17台  │  ⚠️ 警告: 6台  │  ❌ 异常: 2台 │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ 报告类型: ⚫完整报告 ⚪问题报告 ⚪摘要报告  导出格式: ⚫PDF ⚪Excel ⚪Word    │
  │ 包含内容: ☑️检查结果 ☑️问题详情 ☑️修复建议 ☐执行日志                      │
  │ 保存位置: [/home/<USER>/_______________] 📁                             │
  ├─────────────────────────────────────────────────────────────────────────┤
  │ ┌─────────────────────────────────────────────────────────────────────┐ │
  │ │☑️│任务名称            │完成时间    │服务器│合规率│检查结果      │     │ │
  │ ├─────────────────────────────────────────────────────────────────────┤ │
  │ │☑️│等保合规检查-生产环境│2024-01-15  │8台   │85%  │✅6 ⚠️2 ❌0  │     │ │
  │ │☐│等保合规检查-测试环境│2024-01-14  │5台   │78%  │✅3 ⚠️2 ❌0  │     │ │
  │ │☐│网络安全专项检查    │2024-01-13  │12台  │72%  │✅8 ⚠️3 ❌1  │     │ │
  │ └─────────────────────────────────────────────────────────────────────┘ │
  │                                                                         │
  │ 已选择: 1个任务                    ☑️ 全选  ☐ 反选  🔍 仅高合规率任务    │
  └─────────────────────────────────────────────────────────────────────────┘
```

**设计特点:**
- 基于已完成任务的报告导出，更符合实际使用场景
- 配置栏水平排列，紧凑高效的空间利用
- 任务表格显示详细的检查结果统计
- 支持选择多个任务生成综合报告

## 8. 现代消息弹窗

```
                    ┌─────────────────────────────────────┐
                    │ ✅ 操作成功                      ✕ │
                    ├─────────────────────────────────────┤
                    │                                     │
                    │ 服务器连接测试成功！                 │
                    │                                     │
                    │                  确定               │
                    └─────────────────────────────────────┘
```

```
                    ┌─────────────────────────────────────┐
                    │ ⚠️ 确认操作                      ✕ │
                    ├─────────────────────────────────────┤
                    │                                     │
                    │ 确定要删除选中的服务器吗？           │
                    │ 此操作不可撤销。                     │
                    │                                     │
                    │           取消        确定          │
                    └─────────────────────────────────────┘
```

**设计特点:**
- 现代模态对话框设计，带有类型图标和关闭按钮
- 不同类型使用不同的头部颜色（成功-绿色，警告-黄色，错误-红色，信息-蓝色）
- 平滑的动画效果，淡入淡出和滑入动画
- 支持点击遮罩层关闭

## 功能模块说明

### 🎯 **4个核心功能模块**

1. **🖥️ 服务器管理**
   - 添加、编辑、删除目标服务器
   - 支持密码和密钥两种认证方式
   - 连接状态监控和测试
   - 现代表格设计，悬停效果和状态标签

2. **⚙️ 核查配置**
   - 多项内置检查项目，按分类组织
   - 系统、网络、应用、安全、合规五大类别
   - 简单的启用/禁用状态配置
   - 全局配置统一管理，避免重复设置

3. **📋 任务管理**
   - 任务列表显示所有创建的检查任务
   - 统计卡片显示任务概览和状态分布
   - 创建任务对话框，使用全局配置
   - 任务状态管理：待执行、执行中、已完成、失败、取消

4. **📄 报告导出**
   - 基于已完成任务的报告导出
   - 可选择多个任务生成综合报告
   - 多种报告类型：完整报告、问题报告、摘要报告
   - 支持PDF、Excel、Word格式导出

### 📋 **操作流程**

1. **服务器管理** → 添加和管理要检查的目标服务器
2. **核查配置** → 设置全局检查配置，启用需要的检查项目
3. **任务管理** → 创建检查任务，选择服务器执行检查
4. **报告导出** → 基于已完成任务生成和导出检查报告

### ✨ **现代设计特点**

- **现代工具类风格**: 采用现代桌面应用设计语言，专业简洁
- **卡片化设计**: 白色卡片容器，清晰的层次结构和阴影效果
- **统一交互**: 现代按钮样式，微动画反馈，统一的颜色系统
- **原生桌面体验**: 无内置标题栏，使用系统标题栏
- **响应式布局**: 灵活的内容区域，支持内容滚动
- **状态可视化**: 进度条、状态标签、统计卡片等丰富的状态展示

### 🎨 **视觉系统**

- **颜色系统**: 现代蓝色主色调 (#3b82f6)，中性灰色背景
- **间距系统**: 8px基础网格，24px标准间距
- **圆角系统**: 8px卡片圆角，6px按钮圆角，4px小组件圆角
- **阴影系统**: 轻微阴影增加层次感
- **动画系统**: 微动画反馈，0.2s缓动过渡

## 技术实现

### 🛠️ **技术栈**

- **前端框架**: React 18 + Vite
- **桌面框架**: Wails v2 (Go + React)
- **样式系统**: 原生CSS + 现代设计系统
- **状态管理**: React Hooks (useState, useEffect)
- **组件库**: 自定义组件库，现代工具类风格

### 📁 **项目结构**

```
frontend/src/
├── components/
│   ├── Layout.jsx              # 主布局组件
│   ├── ServerManagement.jsx    # 服务器管理
│   ├── CheckConfig.jsx         # 核查配置
│   ├── TaskList.jsx           # 任务管理
│   ├── CreateTaskDialog.jsx   # 创建任务对话框
│   ├── ReportExport.jsx       # 报告导出
│   ├── Modal.jsx              # 通用模态框
│   └── *.css                  # 对应样式文件
├── App.jsx                    # 应用入口
└── main.jsx                   # React入口
```

### 🎯 **核心特性**

1. **模块化设计**: 每个功能模块独立组件，便于维护
2. **统一样式系统**: 共享的设计语言和组件样式
3. **现代交互**: 微动画、悬停效果、状态反馈
4. **响应式布局**: 灵活的内容区域和滚动处理
5. **类型化弹窗**: 替代原生alert，提供更好的用户体验

### 📱 **用户体验优化**

- **任务导向设计**: 从页面式改为对话框式创建任务
- **配置统一管理**: 避免重复配置，全局配置一处设置
- **历史记录管理**: 任务列表显示所有历史任务
- **基于任务的报告**: 报告导出基于已完成任务，更符合实际使用场景
